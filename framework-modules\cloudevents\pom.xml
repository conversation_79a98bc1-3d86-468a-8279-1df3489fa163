<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-modules</artifactId>
        <version>1.0.27</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>cloudevents</artifactId>

    <dependencies>
        <!-- Framework -->
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sns</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sqs</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- CloudEvents -->
        <dependency>
            <groupId>io.cloudevents</groupId>
            <artifactId>cloudevents-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cloudevents</groupId>
            <artifactId>cloudevents-json-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cloudevents</groupId>
            <artifactId>cloudevents-spring</artifactId>
        </dependency>

        <!-- Others -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-localstack-test-support</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>cloudevents</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>false</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>cloudevents</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>false</skip.integrationTests>
            </properties>
        </profile>
    </profiles>

</project>
