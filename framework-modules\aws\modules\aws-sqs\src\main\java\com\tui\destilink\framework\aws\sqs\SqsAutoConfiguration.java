package com.tui.destilink.framework.aws.sqs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sqs.components.FwFifoSqsComponentFactory;
import com.tui.destilink.framework.aws.sqs.components.FwStandardSqsComponentFactory;
import com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties;
import com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties;
import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryableHandlerMethodArgumentResolver;
import com.tui.destilink.framework.aws.sqs.logging.SqsLoggingContextCustomizer;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import io.awspring.cloud.autoconfigure.sqs.SqsProperties;
import io.awspring.cloud.sqs.config.SqsBootstrapConfiguration;
import io.awspring.cloud.sqs.config.SqsListenerConfigurer;
import io.awspring.cloud.sqs.config.SqsMessageListenerContainerFactory;
import io.awspring.cloud.sqs.listener.*;
import io.awspring.cloud.sqs.listener.acknowledgement.handler.AcknowledgementMode;
import io.awspring.cloud.sqs.support.converter.MessagingMessageConverter;
import io.awspring.cloud.sqs.support.converter.SqsMessagingMessageConverter;
import jakarta.annotation.PostConstruct;
import jakarta.validation.ValidationException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@AutoConfiguration
@EnableConfigurationProperties({SqsLoggingProperties.class, DefaultSqsContainerProperties.class})
@ConditionalOnClass({SqsAsyncClient.class, SqsBootstrapConfiguration.class})
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class SqsAutoConfiguration {

    private final SqsProperties sqsProperties;
    private final DefaultSqsContainerProperties defaultSqsContainerProperties;

    @Configuration
    @AllArgsConstructor
    static class SqsConfigurationValidator {

        private final MessageListenerContainerRegistry registry;

        @PostConstruct
        private void validate() {
            Collection<SqsContainerOptions> options = registry.getListenerContainers().stream()
                    .filter(SqsMessageListenerContainer.class::isInstance)
                    .map(SqsMessageListenerContainer.class::cast)
                    .map(AbstractMessageListenerContainer::getContainerOptions)
                    .filter(SqsContainerOptions.class::isInstance)
                    .map(SqsContainerOptions.class::cast)
                    .filter(o ->
                            o.getListenerMode() != ListenerMode.SINGLE_MESSAGE
                                    || !o.getQueueAttributeNames().contains(QueueAttributeName.ALL)
                                    || o.getAcknowledgementMode() != AcknowledgementMode.MANUAL
                    )
                    .collect(Collectors.toSet());
            if (!options.isEmpty()) {
                throw new ValidationException("Only SQS Single Message Mode currently supported and queue attributes must be configured with \"All\" and AckMode with \"MANUAL\"");
            }
        }
    }

    @Bean
    SqsListenerConfigurer configurer() {
        return registrar -> registrar
                .manageMethodArgumentResolvers(b -> b.addFirst(new SqsListenerRetryableHandlerMethodArgumentResolver()));
    }

    @Bean
    public SqsMessageListenerContainerFactory<Object> defaultSqsListenerContainerFactory(
            ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers,
            QueueUrlResolver queueUrlResolver,
            DefaultSqsContainerProperties defaultSqsContainerProperties,
            SqsLoggingProperties sqsLoggingProperties,
            ObjectProvider<SqsAsyncClient> sqsAsyncClient,
            ObjectProvider<ObjectMapper> objectMapperProvider,
            MessagingMessageConverter<?> messagingMessageConverter) {

        SqsMessageListenerContainerFactory<Object> factory = new SqsMessageListenerContainerFactory<>();
        factory.setContainerComponentFactories(List.of(
                new FwFifoSqsComponentFactory<>(loggingContextCustomizers, defaultSqsContainerProperties, sqsLoggingProperties, queueUrlResolver),
                new FwStandardSqsComponentFactory<>(loggingContextCustomizers, defaultSqsContainerProperties, sqsLoggingProperties, queueUrlResolver)));
        factory.configure(this::configureProperties);
        sqsAsyncClient.ifAvailable(factory::setSqsAsyncClient);

        objectMapperProvider.ifAvailable(om -> setMapperToConverter(messagingMessageConverter, om));
        factory.configure(options -> options.messageConverter(messagingMessageConverter));
        return factory;
    }

    private void setMapperToConverter(MessagingMessageConverter<?> messagingMessageConverter, ObjectMapper om) {
        if (messagingMessageConverter instanceof SqsMessagingMessageConverter sqsConverter) {
            sqsConverter.setObjectMapper(om);
        }
    }

    private void configureProperties(SqsContainerOptionsBuilder options) {
        PropertyMapper mapper = PropertyMapper.get().alwaysApplyingWhenNonNull();
        mapper.from(this.sqsProperties.getQueueNotFoundStrategy()).to(options::queueNotFoundStrategy);
        mapper.from(this.sqsProperties.getListener().getMaxConcurrentMessages()).to(options::maxConcurrentMessages);
        mapper.from(this.sqsProperties.getListener().getMaxMessagesPerPoll()).to(options::maxMessagesPerPoll);
        mapper.from(this.sqsProperties.getListener().getPollTimeout()).to(options::pollTimeout);
        options.queueAttributeNames(Set.of(QueueAttributeName.ALL));
        options.listenerShutdownTimeout(defaultSqsContainerProperties.getListenerShutdownTimeout());
        options.acknowledgementShutdownTimeout(defaultSqsContainerProperties.getAcknowledgementShutdownTimeout());
        options.acknowledgementMode(AcknowledgementMode.MANUAL);
    }
}
