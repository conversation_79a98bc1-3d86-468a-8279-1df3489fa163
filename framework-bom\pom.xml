<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tui.destilink.framework</groupId>
    <artifactId>framework-bom</artifactId>
    <version>1.0.27</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.tui.destilink</groupId>
        <artifactId>destilink-ci-parent</artifactId>
        <version>[0.0,1.0)</version>
        <relativePath/>
    </parent>

    <dependencyManagement>
        <dependencies>
            <!-- Parents -->
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>framework-build-parent-ms</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>ms-parent</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.parent</groupId>
                <artifactId>ms-parent</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>framework-build-parent-cronjob</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>cronjob-parent</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.parent</groupId>
                <artifactId>cronjob-parent</artifactId>
                <version>1.0.27</version>
                <type>pom</type>
            </dependency>
            <!-- Framework -->
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>jackson-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>cloudevents</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>ms-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>cronjob-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.async</groupId>
                <artifactId>async-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>caching</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>redis-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.locking</groupId>
                <artifactId>locking-shedlock</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>openapi-generator</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-aurora-postgresql</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-elasticache-redis</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-msk-auth-iam</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-s3</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-sns</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-sqs</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-opensearch</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.file</groupId>
                <artifactId>file-download</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.file</groupId>
                <artifactId>file-storage</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.web</groupId>
                <artifactId>web-core</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.web</groupId>
                <artifactId>web-client</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.web</groupId>
                <artifactId>web-server</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.web</groupId>
                <artifactId>web-security-oauth2-server</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.web</groupId>
                <artifactId>web-openapi</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.resilience</groupId>
                <artifactId>resilience-retry</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.resilience</groupId>
                <artifactId>resilience-circuitbreaker</artifactId>
                <version>1.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-support</groupId>
                <artifactId>test-core</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-support</groupId>
                <artifactId>s-ftp-test-support</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-support</groupId>
                <artifactId>keycloak-test-support</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-support</groupId>
                <artifactId>kafka-test-support</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.aws</groupId>
                <artifactId>aws-localstack-test-support</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-support</groupId>
                <artifactId>redis-test-support</artifactId>
                <version>1.0.27</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>