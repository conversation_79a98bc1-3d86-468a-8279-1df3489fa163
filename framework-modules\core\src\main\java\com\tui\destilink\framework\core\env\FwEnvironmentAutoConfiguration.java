package com.tui.destilink.framework.core.env;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@EnableConfigurationProperties({FwEnvironmentProperties.class})
public class FwEnvironmentAutoConfiguration {

    @Bean
    public FwEnvironmentName fwEnvironmentName(@Value("${" + FwEnvironmentProperties.PREFIX + ".name}") String name) {
        return FwEnvironmentName.of(name);
    }

}
