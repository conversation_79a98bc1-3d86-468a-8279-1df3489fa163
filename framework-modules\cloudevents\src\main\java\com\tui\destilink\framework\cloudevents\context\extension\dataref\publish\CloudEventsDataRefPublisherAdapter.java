package com.tui.destilink.framework.cloudevents.context.extension.dataref.publish;

import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import lombok.Getter;
import org.springframework.lang.NonNull;

import java.net.URI;
import java.util.concurrent.CompletableFuture;

@Getter
public abstract class CloudEventsDataRefPublisherAdapter {

    private final boolean isAsync;

    protected CloudEventsDataRefPublisherAdapter(boolean isAsync) {
        this.isAsync = isAsync;
    }

    public abstract boolean supports(@NonNull CloudEvent cloudEvent, @NonNull URI dataRef);

    public URI publish(@NonNull CloudEventData data, @NonNull URI dataRef, @NonNull String dataContentType) {
        if (isAsync) {
            throw new UnsupportedOperationException("This adapter only supports async operations");
        }
        return doPublish(data, dataRef, dataContentType);
    }

    public CompletableFuture<URI> publishAsync(@NonNull CloudEventData data, @NonNull URI dataRef, @NonNull String dataContentType) {
        if (!isAsync) {
            throw new UnsupportedOperationException("This adapter only supports non-async operations");
        }
        return doPublishAsync(data, dataRef, dataContentType);
    }

    protected URI doPublish(@NonNull CloudEventData data, @NonNull URI dataRef, @NonNull String dataContentType) {
        throw new UnsupportedOperationException();
    }

    protected CompletableFuture<URI> doPublishAsync(@NonNull CloudEventData data, @NonNull URI dataRef, @NonNull String dataContentType) {
        throw new UnsupportedOperationException();
    }

}
