package com.tui.destilink.framework.cloudevents.context.extension.tui.pd;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import lombok.Getter;

@Getter
public enum PdPersonRefSystem {
    ACDS("ACDS"),
    AEP("AEP"),
    ASU("ASU"),
    ATCOM("ATCOM"),
    BRAZE("BRAZE"),
    BENE_DWH("BENE-DWH"),
    CDM("CDM"),
    CP("CP"),
    C4C("C4C"),
    EMAIL("EMAIL"),
    GCA("GCA"),
    GIGYA("GIGYA"),
    GMP("GMP"),
    JUMP("JUMP"),
    KUBA("KUBA"),
    LIME("LIME"),
    MUSEMENT("MUSEMENT"),
    MDM("MDM"),
    MDMADP("MDMADP"),
    TELEPHONE("TELEPHONE"),
    TIM("TIM"),
    TOR("TOR"),
    TRIPS_SALES_ORDER_GUID("TRIPS-SALES-ORDER-GUID"),
    TRIPS_SALES_ORDER_REF("TRIPS-SALES-ORDER-REF"),
    ULTRAPOS("ULTRAPOS"),
    @JsonEnumDefaultValue
    UNKNOWN("UNKNOWN");

    private final String value;

    PdPersonRefSystem(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static PdPersonRefSystem forValue(String value) {
        for (PdPersonRefSystem system : PdPersonRefSystem.values()) {
            if (system.value.equalsIgnoreCase(value)) {
                return system;
            }
        }
        return UNKNOWN;
    }
}
