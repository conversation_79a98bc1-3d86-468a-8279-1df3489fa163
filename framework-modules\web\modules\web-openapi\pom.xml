<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.web</groupId>
        <artifactId>web</artifactId>
        <version>1.0.27</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>web-openapi</artifactId>

    <properties>
        <springdoc-openapi-starter-webmvc-ui.version>2.6.0</springdoc-openapi-starter-webmvc-ui.version>
        <swagger-parser-v3.version>2.1.29</swagger-parser-v3.version>

        <!-- Test dependencies -->
        <test.drh-common-framework.version>1.0.28</test.drh-common-framework.version>
        <test.dbh-acco-reservation-bridge-api.version>2.30</test.dbh-acco-reservation-bridge-api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>openapi-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-security-oauth2-server</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi-starter-webmvc-ui.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser-v3</artifactId>
            <version>${swagger-parser-v3.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>keycloak-test-support</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.drh.framework.aub</groupId>
            <artifactId>aub-client</artifactId>
            <version>${test.drh-common-framework.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink</groupId>
            <artifactId>dbh-acco-reservation-bridge-api</artifactId>
            <version>${test.dbh-acco-reservation-bridge-api.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>dbh-acco-reservation-bridge-api-v2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <!-- Only add to test classpath -->
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                            <addTestCompileSourceRoot>true</addTestCompileSourceRoot>
                            <inputSpec>/destilink/arbs/acco-reservation-bridge-api.yaml</inputSpec>
                            <output>${project.build.directory}/generated-test-sources/dbh-acco-reservation-bridge-api-v2
                            </output>
                            <modelPackage>com.tui.destilink.dbh.acco.reservation.bridge.rest.model.v2</modelPackage>
                            <apiPackage>com.tui.destilink.dbh.acco.reservation.bridge.rest.api.v2</apiPackage>
                            <schemaMappings>
                                <schemaMapping>
                                    BookingStatus=com.tui.destilink.drh.framework.aub.rest.model.v3.common.BookingStatus
                                </schemaMapping>
                                <schemaMapping>
                                    ConfirmationStatus=com.tui.destilink.drh.framework.aub.rest.model.v3.common.ConfirmationStatus
                                </schemaMapping>
                                <schemaMapping>
                                    ResponsePaxDefinition=com.tui.destilink.drh.framework.aub.rest.model.v3.common.ResponsePaxDefinition
                                </schemaMapping>
                                <schemaMapping>
                                    ExternalReferences=com.tui.destilink.drh.framework.aub.rest.model.v3.common.ExternalReferences
                                </schemaMapping>
                                <schemaMapping>
                                    SupplierDetails=com.tui.destilink.drh.framework.aub.rest.model.v3.common.SupplierDetails
                                </schemaMapping>
                                <schemaMapping>Errata=com.tui.destilink.drh.framework.aub.rest.model.v3.common.Errata
                                </schemaMapping>
                                <schemaMapping>Issues=com.tui.destilink.drh.framework.aub.rest.model.v3.common.Issues
                                </schemaMapping>
                                <schemaMapping>
                                    SupplierMessageResponse=com.tui.destilink.drh.framework.aub.rest.model.v3.common.SupplierMessageResponse
                                </schemaMapping>
                                <schemaMapping>
                                    SupplierOfferDetails=com.tui.destilink.drh.framework.aub.rest.model.v3.common.SupplierOfferDetails
                                </schemaMapping>
                                <schemaMapping>
                                    VirtualCreditCards=com.tui.destilink.drh.framework.aub.rest.model.v3.common.VirtualCreditCards
                                </schemaMapping>
                                <schemaMapping>
                                    AbstractDubhIdResponseReservationAction=com.tui.destilink.drh.framework.aub.rest.model.v3.common.AbstractDubhIdResponseReservationAction
                                </schemaMapping>
                                <schemaMapping>
                                    AbstractResponseReservationActionMetaData=com.tui.destilink.drh.framework.aub.rest.model.v3.common.AbstractResponseReservationActionMetaData
                                </schemaMapping>
                            </schemaMappings>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.tui.destilink</groupId>
                        <artifactId>dbh-acco-reservation-bridge-api</artifactId>
                        <version>${test.dbh-acco-reservation-bridge-api.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
