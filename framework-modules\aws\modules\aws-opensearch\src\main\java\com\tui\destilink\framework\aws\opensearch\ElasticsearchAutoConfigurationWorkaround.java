package com.tui.destilink.framework.aws.opensearch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigurationImportFilter;
import org.springframework.boot.autoconfigure.AutoConfigurationMetadata;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;

@Slf4j
public class ElasticsearchAutoConfigurationWorkaround implements AutoConfigurationImportFilter {

    public static final String ELASTICSEARCH_DATA_AUTO_CONFIGURATION = ElasticsearchDataAutoConfiguration.class.getName();

    @Override
    public boolean[] match(String[] autoConfigurationClasses, AutoConfigurationMetadata autoConfigurationMetadata) {
        boolean[] match = new boolean[autoConfigurationClasses.length];
        for (int i = 0; i < autoConfigurationClasses.length; i++) {
            String classFullName = autoConfigurationClasses[i];
            if (ELASTICSEARCH_DATA_AUTO_CONFIGURATION.equals(classFullName)) {
                log.debug("Opensearch is active. Disabling Elastisearch auto configuration.");
                match[i] = false;
                continue;
            }
            match[i] = true;
        }
        return match;
    }

}
