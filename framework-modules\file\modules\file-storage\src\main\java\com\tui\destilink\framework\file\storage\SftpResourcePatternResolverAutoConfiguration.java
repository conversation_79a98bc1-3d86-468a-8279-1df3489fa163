package com.tui.destilink.framework.file.storage;

import com.tui.destilink.framework.file.storage.impl.sftp.SftpConfigurableResourcePatternResolverCustomizer;
import com.tui.destilink.framework.file.storage.impl.sftp.SftpResourceLoaderProcessor;
import com.tui.destilink.framework.file.storage.impl.sftp.SftpSessionService;
import org.apache.sshd.sftp.client.SftpClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnClass(SftpClient.class)
public class SftpResourcePatternResolverAutoConfiguration {

    @Bean
    ConfigurableResourcePatternResolverCustomizer sftpConfigurableResourcePatternResolverCustomizer(SftpSessionService sftpSessionService) {
        return new SftpConfigurableResourcePatternResolverCustomizer(sftpSessionService);
    }

    @Bean
    SftpSessionService sftpSessionService() {
        return new SftpSessionService();
    }

    @Bean
    SftpResourceLoaderProcessor sftpResourceLoaderProcessor(SftpSessionService sftpSessionService) {
        return new SftpResourceLoaderProcessor(sftpSessionService);
    }
}
