package com.tui.destilink.framework.cloudevents.validation;

import com.fasterxml.jackson.databind.node.NullNode;
import com.google.auto.service.AutoService;
import com.tui.destilink.framework.cloudevents.validation.util.ExtensionValidatorUtils;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.extensions.DatarefExtension;
import io.cloudevents.core.validator.CloudEventValidator;
import io.cloudevents.jackson.JsonCloudEventData;

import java.net.URI;
import java.util.Optional;
import java.util.Set;

@SuppressWarnings("unused")
@AutoService(CloudEventValidator.class)
public class DataRefValidator implements CloudEventValidator {

    private static final Set<String> SUPPORTED_SCHEMES = Set.of("s3");

    @Override
    public void validate(CloudEvent cloudEvent) {
        Object value = cloudEvent.getExtension(DatarefExtension.DATAREF);
        Object data = cloudEvent.getData();
        // NullNode is expected for "null" in JSON
        if (data instanceof JsonCloudEventData jcec && jcec.getNode() instanceof NullNode) {
            data = null;
        }
        if (value == null && data == null) {
            throw ExtensionValidatorUtils.INSTANCE.invalidValueException(DatarefExtension.DATAREF, "data or dataref must not be null");
        }
        if (value instanceof String str) {
            // Build URI from string
            value = URI.create(str);
        }
        if (value instanceof URI dataRef) {
            Optional<String> protocol = SUPPORTED_SCHEMES.stream().filter(p -> p.equalsIgnoreCase(dataRef.getScheme())).findFirst();
            if (protocol.isEmpty()) {
                throw ExtensionValidatorUtils.INSTANCE.invalidValueException(DatarefExtension.DATAREF, value, SUPPORTED_SCHEMES, "is not in supported schemes");
            }
            if (data != null) {
                throw ExtensionValidatorUtils.INSTANCE.invalidValueException("data", "data and dataref are mutually exclusive");
            }
        } else if (value != null) {
            throw ExtensionValidatorUtils.INSTANCE.invalidTypeException(DatarefExtension.DATAREF, String.class, value.getClass());
        }
    }
}
