package com.tui.destilink.framework.core.util;

import com.tui.destilink.framework.core.properties.FwCompositePropertySource;
import lombok.experimental.UtilityClass;
import org.springframework.boot.DefaultPropertiesPropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;

import java.util.Set;

@UtilityClass
public class FwPropertySourceUtils {

    /**
     * Returns the FwCompositePropertySource. A new instance is setup up if not already present.
     *
     * @param env The environment to get the property source from
     * @return The FwCompositePropertySource instance
     */
    public static FwCompositePropertySource getPropertySource(ConfigurableEnvironment env) {
        PropertySource<?> defaultPropSource = env.getPropertySources().get(DefaultPropertiesPropertySource.NAME);
        if (defaultPropSource instanceof FwCompositePropertySource fcps) {
            DefaultPropertiesPropertySource.moveToEnd(env);
            return fcps;
        }

        FwCompositePropertySource propertySource = null;
        if (defaultPropSource instanceof DefaultPropertiesPropertySource dpps) {
            propertySource = new FwCompositePropertySource(dpps);
            env.getPropertySources().replace(DefaultPropertiesPropertySource.NAME, propertySource);
        } else if (defaultPropSource == null) {
            propertySource = new FwCompositePropertySource();
            env.getPropertySources().addLast(propertySource);
        }

        if (propertySource == null) {
            throw new IllegalStateException("Cannot create fw property source because existing default is type of "
                    + defaultPropSource.getClass().getName());
        }
        DefaultPropertiesPropertySource.moveToEnd(env);
        return propertySource;
    }

    public static MapPropertySource getFwPropertiesOverridePropertySource(ConfigurableEnvironment env) {
        return getPropertySource(env).getFwPropertiesOverridePropertySource();
    }

    public static DefaultPropertiesPropertySource getDefaultPropertiesPropertySource(ConfigurableEnvironment env) {
        return getPropertySource(env).getDefaultPropsPropSource();
    }

    public static Set<MapPropertySource> getTestPropertySources(ConfigurableEnvironment env) {
        FwCompositePropertySource propertySource = getPropertySource(env);
        return propertySource.getTestPropertySources();
    }

    public static Set<MapPropertySource> getAppTypePropertySources(ConfigurableEnvironment env) {
        FwCompositePropertySource propertySource = getPropertySource(env);
        return propertySource.getAppTypePropertySources();
    }

    public static Set<MapPropertySource> getDefaultPropertySources(ConfigurableEnvironment env) {
        FwCompositePropertySource propertySource = getPropertySource(env);
        return propertySource.getDefaultPropertySources();
    }

    @SuppressWarnings("unused")
    public static void moveToEnd(ConfigurableEnvironment env) {
        // Getter already moves property source to the end
        getPropertySource(env);
    }

}
