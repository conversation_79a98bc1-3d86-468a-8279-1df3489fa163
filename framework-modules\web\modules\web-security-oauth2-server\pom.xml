<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.web</groupId>
        <artifactId>web</artifactId>
        <version>1.0.27</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>web-security-oauth2-server</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>caching</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tui.destilink.framework.resilience</groupId>
            <artifactId>resilience-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-all</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>keycloak-test-support</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>redis-test-support</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
