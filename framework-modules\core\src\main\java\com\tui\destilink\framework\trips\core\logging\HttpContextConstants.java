package com.tui.destilink.framework.trips.core.logging;

import lombok.experimental.UtilityClass;

// Datadog logging attributes for http communication
@UtilityClass
public class HttpContextConstants {

    public static final String PREFIX = "http";
    private static final String URL = "url";
    private static final String URL_DETAILS = "url_details"; // http.url_details will be filled automatically with the target host, path, query string parameters etc.
    private static final String METHOD = "method"; // org.springframework.http.HttpMethod
    private static final String STATUS = "status_code"; // integer!
    private static final String TYPE = "type"; // HttpContextTypeEnum
    private static final String SEPARATOR = ".";
    // mandatory
    public static final String HTTP_URL = PREFIX + SEPARATOR + URL;
    public static final String HTTP_URL_DETAILS = PREFIX + SEPARATOR + URL_DETAILS; // http.url_details will be filled automatically with the target host, path, query string parameters etc.
    public static final String HTTP_METHOD = PREFIX + SEPARATOR + METHOD; // org.springframework.http.HttpMethod
    public static final String HTTP_STATUS = PREFIX + SEPARATOR + STATUS; // integer!
    // recommended
    public static final String HTTP_TYPE = PREFIX + SEPARATOR + TYPE; // HttpContextTypeEnum
}
