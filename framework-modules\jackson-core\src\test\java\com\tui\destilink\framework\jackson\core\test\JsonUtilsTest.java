package com.tui.destilink.framework.jackson.core.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.jackson.core.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@RequiredArgsConstructor(onConstructor_ = @Autowired)
class JsonUtilsTest {

    private final ObjectMapper objectMapper;

    @SneakyThrows
    @Test
    void givenJsonPayloadShouldBeSerialzedToString() {
        final String raw = "{\"name\":\"Bookhub\"}";
        final JsonNode json = objectMapper.readTree(raw);
        final String expected = """
                {
                  "name" : "Bookhub"
                }""";
        assertThat(JsonUtils.writeToString(objectMapper, json, true)).isEqualToNormalizingNewlines(expected);
        assertThat(JsonUtils.writeToString(objectMapper, json, false)).isEqualTo(raw);
        assertThat(JsonUtils.prettyPrintRawJsonIfPossible(objectMapper, raw)).isEqualToNormalizingNewlines(expected);
    }
}
