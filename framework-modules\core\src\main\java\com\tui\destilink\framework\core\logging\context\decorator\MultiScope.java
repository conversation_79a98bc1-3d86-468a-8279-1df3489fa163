package com.tui.destilink.framework.core.logging.context.decorator;

import org.slf4j.MDC;

import java.util.Map;
import java.util.Set;

public class MultiScope implements Scope {

    private final Set<String> keys;
    private final Map<String, String> previousValues;

    protected MultiScope(Set<String> keys, Map<String, String> previousValues) {
        this.keys = keys;
        this.previousValues = previousValues;
    }

    @Override
    public void close() {
        if (keys != null) {
            keys.forEach(MDC::remove);
        }
        if (previousValues != null) {
            previousValues.forEach(MDC::put);
        }
    }
}
