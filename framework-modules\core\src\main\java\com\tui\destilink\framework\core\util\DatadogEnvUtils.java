package com.tui.destilink.framework.core.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class DatadogEnvUtils {

    public static final String DD_AGENT_ENABLED = "DD_AGENT_ENABLED";
    public static final String DD_ENV = "DD_ENV";
    public static final String DD_SERVICE = "DD_SERVICE";
    public static final String DD_VERSION = "DD_VERSION";

    private static final String DD_ENV_DEFAULT = "missing-env";
    private static final String DD_SERVICE_DEFAULT = "missing-service";
    private static final String DD_VERSION_DEFAULT = "0.0.0-missing";

    public static boolean resolveAgentEnabled() {
        String value = System.getenv(DD_AGENT_ENABLED);
        if (value == null) {
            return false;
        }
        return Boolean.parseBoolean(value);
    }

    public static String resolveEnv() {
        return System.getenv(DD_ENV);
    }

    public static String resolveEnvOrDefault() {
        return System.getenv().getOrDefault(DD_ENV, DD_ENV_DEFAULT);
    }

    public static String resolveService() {
        return System.getenv(DD_SERVICE);
    }

    public static String resolveServiceOrDefault() {
        return System.getenv().getOrDefault(DD_SERVICE, DD_SERVICE_DEFAULT);
    }

    public static String resolveVersion() {
        return System.getenv(DD_VERSION);
    }

    public static String resolveVersionOrDefault() {
        return System.getenv().getOrDefault(DD_VERSION, DD_VERSION_DEFAULT);
    }
}
