package com.tui.destilink.framework.core.logging.marker.exception;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fasterxml.jackson.core.JsonGenerator;
import com.google.auto.service.AutoService;
import com.tui.destilink.framework.core.logging.customize.LogbackCustomizer;
import com.tui.destilink.framework.core.logging.customize.jsonprovider.AbstractLogstashMarkersJsonProvider;
import com.tui.destilink.framework.core.logging.util.IThrowableProxyUtils;
import net.logstash.logback.marker.Markers;
import org.slf4j.Marker;

import java.io.IOException;
import java.util.List;

@SuppressWarnings("unused")
@AutoService(LogbackCustomizer.class)
public class ExceptionLogstashMarkersJsonProvider extends AbstractLogstashMarkersJsonProvider {

    @Override
    public void writeTo(JsonGenerator generator, ILoggingEvent iLoggingEvent) throws IOException {
        List<ExceptionMarkerProvider> providers = IThrowableProxyUtils.findNestedExceptions(
                iLoggingEvent.getThrowableProxy(),
                ExceptionMarkerProvider.class);
        if (providers != null && !providers.isEmpty()) {
            final Marker marker = Markers.empty();
            for (ExceptionMarkerProvider p : providers) {
                marker.add(p.getMarker());
            }
            writeLogstashMarkerIfNecessary(generator, marker);
        }
    }
}
