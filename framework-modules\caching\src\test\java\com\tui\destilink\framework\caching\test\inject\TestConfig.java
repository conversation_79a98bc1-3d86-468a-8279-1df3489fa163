package com.tui.destilink.framework.caching.test.inject;

import com.tui.destilink.framework.caching.inject.InjectCachePostProcessor;
import com.tui.destilink.framework.caching.memory.cache.ExpirableConcurrentMapCache;
import org.mockito.Mockito;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.RedisCache;

import java.util.Collection;
import java.util.Map;

@EnableAutoConfiguration(exclude = MetricsAutoConfiguration.class)
@TestConfiguration
public class TestConfig {

    @Bean
    public static InjectCachePostProcessor injectCachePostProcessor() {
        return new InjectCachePostProcessor();
    }

    @Bean
    public CacheManager cacheManager() {
        return new CacheManager() {
            final Map<String, Cache> caches = Map.of(
                    "cache", Mockito.mock(Cache.class),
                    "redis-cache", Mockito.mock(RedisCache.class),
                    "in-memory-cache", Mockito.mock(ExpirableConcurrentMapCache.class));

            @Override
            public Cache getCache(String name) {
                return caches.get(name);
            }

            @Override
            public Collection<String> getCacheNames() {
                return caches.keySet();
            }
        };
    }

}
