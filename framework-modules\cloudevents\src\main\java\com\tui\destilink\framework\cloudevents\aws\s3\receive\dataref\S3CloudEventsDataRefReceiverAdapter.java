package com.tui.destilink.framework.cloudevents.aws.s3.receive.dataref;


import com.tui.destilink.framework.cloudevents.context.extension.dataref.receive.CloudEventsDataRefReceiverAdapter;
import io.awspring.cloud.s3.Location;
import io.cloudevents.CloudEventData;
import io.cloudevents.core.data.BytesCloudEventData;
import org.apache.commons.lang3.tuple.ImmutablePair;
import software.amazon.awssdk.core.async.AsyncResponseTransformer;
import software.amazon.awssdk.services.s3.S3AsyncClient;

import java.net.URI;
import java.util.concurrent.CompletableFuture;

public class S3CloudEventsDataRefReceiverAdapter extends CloudEventsDataRefReceiverAdapter {

    private final S3AsyncClient s3Client;

    public S3CloudEventsDataRefReceiverAdapter(S3AsyncClient s3Client) {
        super(true);
        this.s3Client = s3Client;
    }

    @Override
    public boolean supports(URI dataRef) {
        return "s3".equalsIgnoreCase(dataRef.getScheme());
    }

    @Override
    protected CompletableFuture<ImmutablePair<CloudEventData, String>> doReceiveAsync(URI dataRef) {
        return CompletableFuture.supplyAsync(() -> Location.of(dataRef.toString()))
                .thenCompose(l -> s3Client.getObject(b -> b.bucket(l.getBucket()).key(l.getObject()), AsyncResponseTransformer.toBytes()))
                .thenApply(r -> ImmutablePair.of(BytesCloudEventData.wrap(r.asByteArray()), r.response().contentType()));
    }
}
