<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-build-parent</artifactId>
        <version>1.0.27</version>
        <relativePath>../framework-build-parent/pom.xml</relativePath>
    </parent>
    <artifactId>framework-modules</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>core</module>
        <module>jackson-core</module>
        <module>cloudevents</module>
        <module>aws</module>
        <module>ms-core</module>
        <module>cronjob-core</module>
        <module>file</module>
        <module>test-support</module>
        <module>web</module>
        <module>resilience</module>
        <module>async</module>
        <module>redis-core</module>
        <module>caching</module>
        <module>openapi-generator</module>
        <module>locking</module>
    </modules>
</project>