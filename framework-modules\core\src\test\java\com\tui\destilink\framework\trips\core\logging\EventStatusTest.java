package com.tui.destilink.framework.trips.core.logging;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class EventStatusTest {

    @Test
    void givenEventStatusShouldBeCreatedCorrectly() {
        assertThat(EventStatus.ofName("COMPONENT_COMMANDS_FULLY_SUCCESSFUL")).isEqualTo(EventStatus.COMPONENT_COMMANDS_FULLY_SUCCESSFUL);
        assertThat(EventStatus.ofName("COMPONENT_COMMANDS_FULLY_UNSUCCESSFUL")).isEqualTo(EventStatus.COMPONENT_COMMANDS_FULLY_UNSUCCESSFUL);
        assertThat(EventStatus.ofName("COMPONENT_COMMANDS_PARTIALLY_SUCCESSFUL")).isEqualTo(EventStatus.COMPONENT_COMMANDS_PARTIALLY_SUCCESSFUL);
        assertThat(EventStatus.ofName("UNKNOWN")).isNull();
    }
}
