package com.tui.destilink.framework.core.logging.context.util;

import io.micrometer.context.ContextRegistry;
import io.micrometer.context.ContextSnapshot;
import io.micrometer.context.ContextSnapshotFactory;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ContextSnapshotUtils {

    public static final boolean CLEAR_MISSING_DEFAULT = true;

    public static final ContextSnapshotFactory DEFAULT_SNAPSHOT_FACTORY = ContextSnapshotFactory.builder()
            .contextRegistry(ContextRegistry.getInstance())
            .clearMissing(CLEAR_MISSING_DEFAULT)
            .build();

    public static ContextSnapshot capture() {
        return DEFAULT_SNAPSHOT_FACTORY.captureAll();
    }
}
