package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.download.impl.FtpFileDownloadServiceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.integration.ftp.session.FtpSession;

@RequiredArgsConstructor
@AutoConfiguration
@ConditionalOnClass(FtpSession.class)
public class FtpFileDownloadServiceAutoConfiguration {

    @Bean
    AbstractFileDownloadServiceFactory ftpFileDownloadServiceFactory() {
        return new FtpFileDownloadServiceFactory();
    }
}
