package com.tui.destilink.framework.resilience.retry;

import io.netty.handler.timeout.ReadTimeoutException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.net.SocketTimeoutException;
import java.net.URI;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient(timeout = "PT10S")
@ActiveProfiles("it")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
class RetryResilienceOnControllerIT {

    private final WebTestClient webTestClient;

    @SpyBean
    private final PrebookService prebookService;

    @SneakyThrows
    @Test
    void givenPrebookRequestLeadsToServerErrorWithDefaultRetry() {

        doThrow(WebClientResponseException.create(503, "test", null, null, null)).when(prebookService).process(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebook")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService, times(4)).process(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestLeadsToServerErrorWithDefaultRetryDueToSpecificPredicateException() {
        doThrow(new IllegalArgumentException("doRetry")).when(prebookService).process(any());
        CompositeRetryPredicate.registerRetryPredicate(t -> "doRetry".equals(t.getMessage()));

        this.webTestClient.post()
                .uri("/reservations/testPrebook")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService, times(4)).process(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestLeadsToServerErrorNoopRetry() {
        doThrow(WebClientResponseException.create(500, "test", null, null, null)).when(prebookService).processNoRetry(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebookNoRetry")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService).processNoRetry(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestLeadsToBadRequestWithNoRetry() {
        doThrow(WebClientResponseException.create(401, "test", null, null, null)).when(prebookService).process(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebookBadRequest")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.BAD_REQUEST);
        verify(prebookService).process(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestLeadsToServerErrorWithSpecificRetryConfig() {
        doThrow(WebClientResponseException.create(500, "test", null, null, null)).when(prebookService).processSpecificRetry(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebookSpecific")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService, times(2)).processSpecificRetry(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestShouldBeProcessedSuccessfully() {

        this.webTestClient.post()
                .uri("/reservations/testPrebook")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isOk();
        verify(prebookService).process(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestShouldNotLeadToRetryDueToNettyReadTimeout() {
        doThrow(new WebClientRequestException(new ReadTimeoutException(), HttpMethod.POST, URI.create("/reservations/testPrebookSpecific"), HttpHeaders.EMPTY)).when(prebookService).processSpecificRetry(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebookSpecific")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService).processSpecificRetry(any());
    }

    @SneakyThrows
    @Test
    void givenPrebookRequestShouldNotLeadToRetryDueToSocketTimeout() {
        doThrow(new ResourceAccessException("test", new SocketTimeoutException())).when(prebookService).processSpecificRetry(any());

        this.webTestClient.post()
                .uri("/reservations/testPrebookSpecific")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue("only a test"))
                .exchange()
                .expectStatus()
                .isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        verify(prebookService).processSpecificRetry(any());
    }
}
