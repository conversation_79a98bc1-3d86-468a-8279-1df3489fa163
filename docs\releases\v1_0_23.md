# [v1.0.23](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.23?ref_type=tags)

> Related
>
drh-common-framework [v1.0.26](https://source.tui/sourcing-hotels/destilink/destilink-reservation-handling/drh-common-framework/-/tree/v1.0.26?ref_type=tags)

### :question: Upgrade Steps

- Reenable layer and dsgvo flag context logging to set the following new configs:
    ```yaml
    destilink:
      fw:
        core:
          logging:
            context:
              dsgvo:
                enabled: true
                tag: reservation-handling-dsgvo
              layer: DUBH-TRIPS-Translator
    ```

  **Note that this step is crucial especially for reservation-handling to avoid dsgvo issues and therefore dsgvo tag
  configs will be forced in drh-common-framework in upcoming releases!**

### :raised_hand: Breaking Changes

- Changed parameter of functional
  interface [SqsLoggingContextCustomizer](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/aws/modules/aws-sqs/src/main/java/com/tui/destilink/framework/aws/sqs/logging/SqsLoggingContextCustomizer.java?ref_type=tags)
  from *MessagingMessageHeaders* to *Message<?>*
- Removed ConfigurationProperties *WebServerProperties* in web-server module
    - prefix: **destilink.fw.web.server**
    - Layer and dsgvo flag configs were migrated to core module!
- Removed ConfigurationProperties *SqsLoggingProperties* in aws-sqs module
    - prefix: **destilink.fw.aws.sqs.logging**
    - Layer and dsgvo flag configs were migrated to core module!
- Removed module *trips-core*
    - **Note that all classes were moved unchanged to core module with the same packages to avoid code changes!**

### :star: New Features

- New
  module [cloudevents](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.23/modules/cloudevents?ref_type=tags)
  ```xml
  <dependency>
      <groupId>com.tui.destilink.framework</groupId>
      <artifactId>cloudevents</artifactId>
  </dependency>
  ```

  General implementation for receiving and publishing cloudEvents v2.1 with support for dataref extension (Check-Claim
  pattern using S3 storage).
  SQS/SNS integration is already available and Kafka integration is planned for future releases.

  See [CloudEventsMessageReceiver](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/cloudevents/src/main/java/com/tui/destilink/framework/cloudevents/receive/CloudEventsMessageReceiver.java?ref_type=tags)
  and [CloudEventsPublisher](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/cloudevents/src/main/java/com/tui/destilink/framework/cloudevents/publish/CloudEventsPublisher.java?ref_type=tagss)
  for further details.

  **Consumer example**
  ```java
  @Trace(operationName = "receiveMessage", resourceName = "receiveMultiCommitResponse")
  @SqsListener(value = "${drh.purchase-order-service.input.queue-name}")
  public void receiveMessageFromQueue(Acknowledgement acknowledgment, @Payload MultiCommitResponse payload) {
      try {
          multiCommitResponseProcessor.process(payload);
          acknowledgment.acknowledge();
      } catch (final CloudEventsException ex) {
          log.error(MonitoringMarkerUtils.appendForMonitoringIssue(BaseIssues.EXTERNAL_SNS, Mood.MR_HANKEY), "Error while sending message", ex); // trigger sqs retry in case of sns sending issues
          TracingUtils.setErrorOnSpan(ex, log);
      }
  }
  ```

  **Publisher example**
  ```java
  @Bean
  SnsCloudEventsPublisherContext<CommandsValidationResponse> commandsValidationPublisherContext(SnsCloudEventsPublisherContextFactory snsCloudEventsPublisherContextFactory, TripsBridgeConfig tripsBridgeConfig) {
          return snsCloudEventsPublisherContextFactory
                                                      .builder(CommandsValidationResponse.class, tripsBridgeConfig.getValidation().getOutput().getTopicName())
                                                      .build();
  }

  @Component
  @RequiredArgsConstructor(onConstructor_ = @Autowired)
  public class CommandsValidationResponseSnsProducer implements SnsProducer<MultiPreviewResponse> {

      private final CommandsValidationResponseMapper commandsValidationResponseMapper;
      private final SnsCloudEventsPublisherContext<CommandsValidationResponse> snsCloudEventsPublisherContext;
      private final CloudEventsPublisher cloudEventsPublisher;

      @Override
      public SnsCloudEventsPublishResult sendResponse(MultiPreviewResponse commandsResponse, CommandsMetaData metaData, ActionIssues actionIssues) {
          final CommandsValidationResponse response = commandsValidationResponseMapper.mapToResponse(commandsResponse, metaData, actionIssues);

          return cloudEventsPublisher.buildRequest(snsCloudEventsPublisherContext, response).publish();
      }
  }
  ```

    - Compile dependencies:
        - aws-sns module
        - aws-sqs module
        - aws-s3 module
        - io.cloudevents:cloudevents-core
        - io.cloudevents:cloudevents-json-jackson
        - io.cloudevents:cloudevents-spring

- [MonitoringMarkerUtils](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/core/src/main/java/com/tui/destilink/framework/core/logging/monitoring/MonitoringMarkerUtils.java?ref_type=tags)
  convenience class to set logstash marker for datadog monitoring facets.
    - Please take a brief look at our new mood feature and also look for MR. Hankey :poop:
      ```java
      log.debug(MonitoringMarkerUtils.appendForMonitoringIssue(BaseIssues.EXTERNAL_SNS, Mood.MR_HANKEY), "Hello, Mr. Hankey");
      ```
- drh-common-framework
    - New SqsLoggingContextCustomizer for processing MultiCommitResponses and evaluating the following attributes:
        - proxyRequestType
        - tuiSalesBookingRef
        - traceCode
        - legalEntity

### :bug: Bug Fixes

- drh-common-framework:
    - Map again *MonetaryAmount* to moneta *Money* in AUB to use *BigDecimal* and currency type for money fields
- [DLI-6371](https://jira.tuigroup.com/browse/DLI-6371): Service startup despite communication issues with
  cloud-config-service

### :fire: Improvements

- Introduced *ContextDecorator* fluent API for simple managing *MDC* for context logging (
  see  [AbstractContextDecorator](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/core/src/main/java/com/tui/destilink/framework/core/logging/context/decorator/AbstractContextDecorator.java?ref_type=tags)).
  This API is a typesafe general convenience implementation to avoid low level access to *MDC* and to put related
  logging attributes together at exactly one place.

  Note that it is also a replacement
  for [MDCPrefixWrapper](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/core/src/main/java/com/tui/destilink/framework/trips/core/logging/MDCPrefixWrapper.java?ref_type=tags)
  which will be deprecated in upcoming releases.

  **Usage**
  ```java
  TripsContextDecorator.getInstance().getBusinessProcessId().put(UUID.randomUUID());
  final String businessProcessId = TripsContextDecorator.getInstance().getBusinessProcessId().get();
  ```

- aws-sqs
    - Improved SQS tracing to support new datadog Data Streaming feature
    - Added common exception handling for SQS listener
        - **It's therefore no longer necessary to catch unknown exceptions globally in all listener implementations!**
    - Cleaned and improved context logging of incoming and outgoing ( for cloudEvents only!) events
        - Message payload and attributes as well as cloudEvent meta data will be logged as facets!
    - New handler beans for deleting Events and for moving Events directly to DLQ (
      see  [SqsResolverAutoConfiguration](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.23/modules/aws/modules/aws-sqs/src/main/java/com/tui/destilink/framework/aws/sqs/SqsResolverAutoConfiguration.java?ref_type=tags))