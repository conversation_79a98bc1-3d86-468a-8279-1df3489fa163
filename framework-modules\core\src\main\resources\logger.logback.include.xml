<included xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xmlns="http://ch.qos.logback/xml/ns/logback"
          xsi:schemaLocation="http://ch.qos.logback/xml/ns/logback">
    <!--Logger-->

    <logger name="com.tui.destilink" level="info"/>

    <logger name="org.springframework.web" level="info"/>
    <logger name="org.springframework.cloud.config" level="info"/>
    <logger name="org.springframework.cloud.config.client.ConfigServicePropertySourceLocator" level="warn"/>

    <logger name="org.hibernate.SQL" level="info"/>
    <logger name="org.hibernate.type" level="info"/>
    <logger name="org.hibernate.transaction" level="debug"/>
    <logger name="org.hibernate.stat" level="debug"/>
    <logger name="org.hibernate.cache.ehcache.internal.regions.EhcacheGeneralDataRegion" level="debug"/>

    <logger name="org.apache.http.impl.conn" level="info"/>
    <logger name="org.apache.http.impl.client" level="info"/>
    <logger name="org.apache.http.client" level="info"/>
    <logger name="org.apache.http.wire" level="error"/>

</included>