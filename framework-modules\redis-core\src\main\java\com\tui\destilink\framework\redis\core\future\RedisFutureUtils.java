package com.tui.destilink.framework.redis.core.future;

import io.lettuce.core.RedisFuture;
import lombok.experimental.UtilityClass;

import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

@UtilityClass
public class RedisFutureUtils {

    public static <T> RedisFuture<T> unwrapCompletableFuture(CompletableFuture<RedisFuture<T>> completableFuture) {
        try {
            return completableFuture.join();
        } catch (CancellationException ex) {
            return new FailedRedisFuture<>(ex);
        } catch (CompletionException ex) {
            return new FailedRedisFuture<>(ex.getCause());
        }
    }

}
