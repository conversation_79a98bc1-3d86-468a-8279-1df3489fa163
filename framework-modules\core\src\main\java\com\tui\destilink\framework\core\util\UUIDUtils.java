package com.tui.destilink.framework.core.util;

import com.fasterxml.uuid.Generators;
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator;
import com.fasterxml.uuid.impl.UUIDUtil;
import lombok.experimental.UtilityClass;

import java.util.UUID;

@UtilityClass
public class UUIDUtils extends UUIDUtil {

    private static final TimeBasedEpochGenerator UUIDv7 = Generators.timeBasedEpochGenerator();

    public static UUID generateUUIDv7() {
        return UUIDv7.generate();
    }
}
