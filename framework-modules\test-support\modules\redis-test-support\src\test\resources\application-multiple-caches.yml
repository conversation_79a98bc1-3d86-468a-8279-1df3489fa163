destilink:
  fw:
    caching:
      caches:
        defaultCache: { }
        sharedLock:
          keyspace-prefix: "sharedlock:"
          type: java.lang.String
          use-compression: false
        sharedLockShortExpiration:
          keyspace-prefix: "sharedlock:"
          ttl: PT5S
        allowNullValues:
          allow-null-values: true
        enableTimeToIdle:
          enable-time-to-idle: true
        cacheWithType:
          type: com.tui.destilink.framework.test.support.redis.it.caching.CachableTestObject
          use-compression: false
        cacheWithoutType:
          use-compression: false
      backend-bindings:
        - type: redis
        - keyspace-prefix: "sharedlock:"
          type: redis