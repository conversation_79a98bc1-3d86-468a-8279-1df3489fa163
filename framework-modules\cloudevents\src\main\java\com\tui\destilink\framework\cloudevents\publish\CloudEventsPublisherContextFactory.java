package com.tui.destilink.framework.cloudevents.publish;

import com.tui.destilink.framework.cloudevents.config.CloudEventsConfigProperties;
import com.tui.destilink.framework.cloudevents.config.CloudEventsConfigProperties.PublisherProperties;
import com.tui.destilink.framework.cloudevents.context.attribute.dataschema.CloudEventsDataSchemaResolver;
import com.tui.destilink.framework.cloudevents.context.attribute.type.CloudEventsTypeResolver;
import com.tui.destilink.framework.core.util.DatadogEnvUtils;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

import java.net.URI;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.function.Function;

public abstract class CloudEventsPublisherContextFactory {

    private final URI defaultSource = buildDefaultSource();

    private final Validator validator;
    private final CloudEventsTypeResolver typeResolver;
    private final CloudEventsDataSchemaResolver dataSchemaResolver;
    private final CloudEventsConfigProperties.PublisherProperties publisherProps;

    protected CloudEventsPublisherContextFactory(Validator validator, CloudEventsTypeResolver typeResolver, CloudEventsDataSchemaResolver dataSchemaResolver, PublisherProperties publisherProps) {
        this.validator = validator;
        this.typeResolver = typeResolver;
        this.dataSchemaResolver = dataSchemaResolver;
        this.publisherProps = publisherProps;
    }

    protected <T> String resolveType(Class<T> payloadClazz) {
        return typeResolver.resolveOptional(payloadClazz)
                .orElseThrow(() -> new NoSuchElementException("No cloudevents type found for class " + payloadClazz));
    }

    protected <T> URI resolveDataSchema(Class<T> payloadClazz) {
        return dataSchemaResolver.resolveOptional(payloadClazz)
                .orElseThrow(() -> new NoSuchElementException("No cloudevents dataSchema found for class " + payloadClazz)).getDataSchema();
    }

    protected URI defaultSource() {
        return defaultSource;
    }

    protected boolean supportsDataRef() {
        return publisherProps.getAllowDataRef();
    }

    protected <T> Function<T, Set<ConstraintViolation<T>>> defaultDataValidator() {
        return validator::validate;
    }

    private static URI buildDefaultSource() {
        String builder = "/accommodation-sourcing"
                + "/" + DatadogEnvUtils.resolveEnvOrDefault()
                + "/" + DatadogEnvUtils.resolveServiceOrDefault()
                + "/" + DatadogEnvUtils.resolveVersionOrDefault();
        return URI.create(builder);
    }
}
