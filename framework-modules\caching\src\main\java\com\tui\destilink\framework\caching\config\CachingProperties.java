package com.tui.destilink.framework.caching.config;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Validated
@ConfigurationProperties(prefix = CachingProperties.PROPERTIES_PREFIX, ignoreUnknownFields = false)
public class CachingProperties implements InitializingBean {

    public static final String PROPERTIES_PREFIX = "destilink.fw.caching";

    @Valid
    @NotNull
    private final CacheDefaults cacheDefaults;

    @NotNull
    private final Map<String, @Valid CacheEntry> caches;

    @Valid
    @NotNull
    private final List<BackendBinding> backendBindings;

    // Only include this as workaround to allow ignoreUnknownFields=false
    private final Map<String, Object> backends;

    public CachingProperties(CacheDefaults cacheDefaults, Map<String, CacheEntry> caches, List<BackendBinding> backendBindings, Map<String, Object> backends) {
        this.cacheDefaults = cacheDefaults;
        this.caches = caches != null ? ImmutableMap.copyOf(caches) : new HashMap<>();
        this.backends = backends;
        this.backendBindings = backendBindings != null ? ImmutableList.copyOf(backendBindings) : List.of();
        // Set default keyspace prefix if is null
        this.backendBindings.stream()
                .filter(bb -> bb.keyspacePrefix == null)
                .forEach(bb -> bb.setKeyspacePrefix(cacheDefaults.getKeyspacePrefix()));
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, List<BackendBinding>> result = backendBindings.stream()
                .collect(Collectors.groupingBy(BackendBinding::getKeyspacePrefix));
        if (result.values().stream().anyMatch(l -> l.size() > 1)) {
            throw new IllegalArgumentException("No duplicate keyspace prefixes are allowed in backendBindings");
        }
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CacheDefaults {
        @NotNull
        @Pattern(regexp = "^\\S*:$")
        private String keyspacePrefix;
        @NotNull
        @Builder.Default
        private Duration ttl = Duration.ofHours(24);
        @NotNull
        @Builder.Default
        private Boolean enableTimeToIdle = Boolean.FALSE;
        @NotNull
        @Builder.Default
        private Boolean allowNullValues = Boolean.FALSE;
        @NotNull
        @Builder.Default
        private Boolean useCompression = Boolean.TRUE;
        @NotNull
        @Builder.Default
        private Boolean allowRuntimeCacheCreation = Boolean.TRUE;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CacheEntry {

        /**
         * This constructor is just a workaround. Spring normally deserializes configuration properties automatically
         * but if the object is empty spring just provides an empty LinkedHashMap<?,?> and cannot create an empty
         * object itself.
         *
         * @param linkedHashMap CAUTION this must be a LinkedHashMap<?,?> and not a more generic Map<?,?>
         */
        public CacheEntry(LinkedHashMap<?, ?> linkedHashMap) { //NOSONAR
            Assert.isTrue(linkedHashMap.isEmpty(), "This constructor is just a workaround and the map should be empty");
        }

        @Pattern(regexp = "^\\S*:$")
        private String keyspacePrefix;
        private Duration ttl;
        private Boolean enableTimeToIdle;
        private Boolean allowNullValues;
        private Boolean useCompression;
        @Builder.Default
        private Class<?> type = null;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BackendBinding {
        @Pattern(regexp = "^\\S*:$")
        private String keyspacePrefix;
        @NotNull
        private BackendBindingType type;
    }

    public enum BackendBindingType {
        REDIS,
        IN_MEMORY
    }

    public boolean hasBackendForType(BackendBindingType type) {
        return backendBindings.stream()
                .anyMatch(bb -> bb.type.equals(type));
    }

    public CacheDefaults copyCacheDefaults() {
        return getCacheDefaults().toBuilder().build();
    }

    public Map<String, CacheEntry> copyCachesWithDefaults() {
        final Map<String, CacheEntry> c = new HashMap<>();
        for (Map.Entry<String, CacheEntry> entry : caches.entrySet()) {
            c.put(entry.getKey(), enrichCopyWithDefaults(entry.getValue()));
        }
        return c;
    }

    public List<BackendBinding> copyBackendBindingsWithDefaults() {
        List<BackendBinding> listCopy = new ArrayList<>(backendBindings.size());
        backendBindings.stream()
                .map(bb -> bb.toBuilder().build())
                .map(this::enrichKeyspacePrefix)
                .forEach(listCopy::add);
        return listCopy;
    }

    public CacheEntry enrichCopyWithDefaults(CacheEntry entry) {
        final CacheEntry result = entry.toBuilder().build();
        enrichWithDefault(cacheDefaults, result);
        result.setType(entry.type);
        return result;
    }

    public static void enrichWithDefault(CacheDefaults defaults, CacheEntry cache) {
        cache.setKeyspacePrefix(getOrDefault(cache.keyspacePrefix, defaults.keyspacePrefix));
        cache.setTtl(getOrDefault(cache.ttl, defaults.ttl));
        cache.setEnableTimeToIdle(getOrDefault(cache.enableTimeToIdle, defaults.enableTimeToIdle));
        cache.setAllowNullValues(getOrDefault(cache.allowNullValues, defaults.allowNullValues));
        cache.setUseCompression(getOrDefault(cache.useCompression, defaults.useCompression));
    }

    private BackendBinding enrichKeyspacePrefix(BackendBinding backendBinding) {
        backendBinding.keyspacePrefix = getOrDefaultKeyspacePrefix(backendBinding.keyspacePrefix);
        return backendBinding;
    }

    private String getOrDefaultKeyspacePrefix(String keyspacePrefix) {
        if (keyspacePrefix == null || keyspacePrefix.isBlank()) {
            return cacheDefaults.keyspacePrefix;
        }
        return keyspacePrefix;
    }

    private static <T> T getOrDefault(T value, T defaultValue) {
        if (value != null) {
            return value;
        }
        return defaultValue;
    }

}
