package com.tui.destilink.framework.redis.core.converter;

import org.springframework.core.convert.converter.Converter;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class GenericKeyConverter implements Converter<Object, String> {

    @Override
    public String convert(Object source) {
        if (source == null) {
            return null;
        }
        return UUID.nameUUIDFromBytes(source.toString().getBytes(StandardCharsets.UTF_8)).toString();
    }


}
