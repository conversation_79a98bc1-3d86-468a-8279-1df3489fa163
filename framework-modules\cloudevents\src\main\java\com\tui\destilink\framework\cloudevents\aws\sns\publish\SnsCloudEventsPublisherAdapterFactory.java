package com.tui.destilink.framework.cloudevents.aws.sns.publish;

import com.tui.destilink.framework.cloudevents.config.CloudEventsConfigProperties;
import com.tui.destilink.framework.cloudevents.context.extension.dataref.publish.CloudEventsDataRefPublisher;
import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublishResult;
import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherAdapter;
import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherAdapterFactory;
import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherContext;
import org.springframework.util.Assert;
import software.amazon.awssdk.services.sns.SnsClient;

public class SnsCloudEventsPublisherAdapterFactory extends CloudEventsPublisherAdapterFactory {

    private final SnsClient snsClient;

    public SnsCloudEventsPublisherAdapterFactory(SnsClient snsClient) {
        this.snsClient = snsClient;
    }

    @Override
    public <T, C extends CloudEventsPublisherContext<T, R>, R extends CloudEventsPublishResult> boolean supports(C context) {
        return context instanceof SnsCloudEventsPublisherContext;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected <T, C extends CloudEventsPublisherContext<T, R>, R extends CloudEventsPublishResult> CloudEventsPublisherAdapter<?, ?, ?> createInstance(C context, CloudEventsDataRefPublisher dataRefPublisher, CloudEventsConfigProperties.PublisherProperties publisherProps) {
        Assert.isInstanceOf(SnsCloudEventsPublisherContext.class, context, "Context must be type of SnsCloudEventsPublisherContext");
        // This cast is safe due to the previous check
        return new SnsCloudEventsPublisherAdapter<>(snsClient, (SnsCloudEventsPublisherContext<T>) context, dataRefPublisher, publisherProps);
    }
}
