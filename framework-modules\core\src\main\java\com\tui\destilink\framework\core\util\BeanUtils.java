package com.tui.destilink.framework.core.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.DefaultSingletonBeanRegistry;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.util.Assert;

@Slf4j
@UtilityClass
public class BeanUtils {

    public static <T extends DisposableBean> T registerDisposableBean(ConfigurableApplicationContext context, String beanName, T bean) {
        Assert.isInstanceOf(DefaultSingletonBeanRegistry.class, context.getBeanFactory());
        return registerDisposableBean(context.getBeanFactory(), beanName, bean);
    }

    @SuppressWarnings("unchecked")
    public static <T extends DisposableBean> T registerDisposableBean(ConfigurableListableBeanFactory beanFactory, String beanName, T bean) {
        try {
            beanFactory.registerSingleton(beanName, bean);
            ((DefaultSingletonBeanRegistry) beanFactory).registerDisposableBean(beanName, bean);
            return (T) beanFactory.initializeBean(bean, beanName);
        } catch (Exception ex) {
            log.error("Failed to initialize bean {}", beanName, ex);
            throw new IllegalStateException("Failed to initialize bean " + beanName, ex);
        }
    }

    public static <T> void registerBean(ConfigurableApplicationContext context, String beanName, T bean) {
        Assert.isInstanceOf(DefaultSingletonBeanRegistry.class, context.getBeanFactory());
        registerBean(context.getBeanFactory(), beanName, bean);
    }

    public static <T> void registerBean(ConfigurableListableBeanFactory beanFactory, String beanName, T bean) {
        try {
            beanFactory.registerSingleton(beanName, bean);
            beanFactory.initializeBean(bean, beanName);
        } catch (Exception ex) {
            log.error("Failed to initialize bean {}", beanName, ex);
            throw new IllegalStateException("Failed to initialize bean " + beanName, ex);
        }
    }

}
