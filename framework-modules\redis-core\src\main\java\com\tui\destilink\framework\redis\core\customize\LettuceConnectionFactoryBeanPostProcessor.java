package com.tui.destilink.framework.redis.core.customize;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

public class LettuceConnectionFactoryBeanPostProcessor implements BeanPostProcessor {

    private final ObjectProvider<LettuceConnectionFactoryCustomizer> customizers;

    public LettuceConnectionFactoryBeanPostProcessor(ObjectProvider<LettuceConnectionFactoryCustomizer> customizers) {
        this.customizers = customizers;
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof LettuceConnectionFactory lcf) {
            customizers.orderedStream().forEach(c -> c.customize(lcf));
        }
        return bean;
    }
}
