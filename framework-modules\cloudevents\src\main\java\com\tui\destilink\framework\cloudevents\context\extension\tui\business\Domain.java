package com.tui.destilink.framework.cloudevents.context.extension.tui.business;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum Domain {
    ANALYTICS("analytics"),
    AVIATION("aviation"),
    COLLEAGUE_COMPUTING("colleague-computing"),
    COMMERCIAL("commercial"),
    CONTENT("content"),
    CORPORATE("corporate"),
    CUSTOMER("customer"),
    ENABLERS("enablers"),
    FLIGHT_AND_DRIVE_SOURCING("flight-and-drive-sourcing"),
    GDN("gdn"),
    HOTELS_AND_RESORTS("hotels-and-resorts"),
    HOTELS_SOURCING("hotels-sourcing"),
    MOBILE("mobile"),
    PRODUCT("product"),
    RETAIL("retail"),
    SECURITY("security"),
    TUI_MM("tui-mm"),
    TUI_FLIGHT_MARKET("tui-flight-market"),
    WEB_AND_THIRD_PARTY_API("web-and-third-party-api"),
    @JsonEnumDefaultValue
    UNKNOWN("unknonwn");

    private final String value;

    Domain(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static Domain forValue(String value) {
        for (Domain domain : Domain.values()) {
            if (domain.value.equalsIgnoreCase(value)) {
                return domain;
            }
        }
        return UNKNOWN;
    }
}
