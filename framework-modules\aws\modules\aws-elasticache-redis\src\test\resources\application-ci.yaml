spring:
  application:
    name: destilink-ci-test-app
destilink:
  fw:
    aws:
      elasticache:
        redis:
          host: clustercfg.framework-ci-tests-valkey-cluster.iuv9e8.euc1.cache.amazonaws.com
          iam-auth:
            replication-group-id: framework-ci-tests-valkey-cluster
            user-id: svc-destilink-ci-test-app
    caching:
      caches:
        cache1:
          type: java.lang.String
          use-compression: false
          ttl: PT10S