package com.tui.destilink.framework.cloudevents.context.extension.tui;

import io.cloudevents.CloudEventExtension;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.experimental.UtilityClass;

import java.util.Set;

@UtilityClass
public class ExtensionsValidateUtils {

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    public static <T extends CloudEventExtension> T validateThrows(T extension) throws ConstraintViolationException {
        Set<ConstraintViolation<T>> result = validate(extension);
        if (result.isEmpty()) {
            return extension;
        }
        throw new ConstraintViolationException(result);
    }

    public static <T extends CloudEventExtension> Set<ConstraintViolation<T>> validate(T extension) {
        return VALIDATOR.validate(extension);
    }
}
