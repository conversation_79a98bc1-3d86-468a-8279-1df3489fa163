<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>destilink-framework</artifactId>
        <version>1.0.27</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>framework-aggregated-report</artifactId>
    <packaging>pom</packaging>

    <properties>
        <!-- Skip sonar analysis -->
        <sonar.skip>false</sonar.skip>
        <dependency-check-maven.skip>true</dependency-check-maven.skip>
        <!-- Do not install and deploy test-only modules -->
        <maven.install.skip>true</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>1.0.27</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tui.destilink.framework.test-application</groupId>
                <artifactId>framework-test-applications</artifactId>
                <version>1.0.27</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>cloudevents</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>cronjob-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>caching</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>redis-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.locking</groupId>
            <artifactId>locking-shedlock</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>openapi-generator</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.resilience</groupId>
            <artifactId>resilience-retry</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.resilience</groupId>
            <artifactId>resilience-circuitbreaker</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-server</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-security-oauth2-server</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-openapi</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-aurora-postgresql</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-elasticache-redis</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-msk-auth-iam</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-localstack-test-support</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-s3</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sns</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sqs</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-opensearch</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.file</groupId>
            <artifactId>file-download</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.file</groupId>
            <artifactId>file-storage</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>redis-test-support</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>s-ftp-test-support</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>keycloak-test-support</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>kafka-test-support</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.async</groupId>
            <artifactId>async-core</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>core-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>aws-localstack-core-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>aws-localstack-messaging-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>file-storage-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>file-download-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>caching-standalone-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>redis-test-support-standalone-tests</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-application</groupId>
            <artifactId>caching-ec-redis-test-support-tests</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>