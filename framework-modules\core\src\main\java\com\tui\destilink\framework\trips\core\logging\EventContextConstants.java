package com.tui.destilink.framework.trips.core.logging;

import lombok.experimental.UtilityClass;

// Datadog logging attributes for events

// Note: for simplicity these constants should not be used as of now.
// Decision might change to set individually values if needed.

// Workaround is to define a datadog parser that creates them:
// TripsContextConstants.BUSINESS_PROCESS_NAME -> EventContextConstants.NAME
// TripsContextConstants.EVENT_VERSION -> EventContextConstants.VERSION
@UtilityClass
public class EventContextConstants {
    public static final String PREFIX = "evt";
    private static final String NAME = "name";
    private static final String VERSION = "version";
    private static final String OUTCOME = "outcome";
    private static final String SEPARATOR = ".";
    // mandatory
    public static final String EVT_NAME = PREFIX + SEPARATOR + NAME;
    public static final String EVT_VERSION = PREFIX + SEPARATOR + VERSION;
    // recommended
    public static final String EVT_OUTCOME = PREFIX + SEPARATOR + OUTCOME; // typically "success" or "failure"
}
