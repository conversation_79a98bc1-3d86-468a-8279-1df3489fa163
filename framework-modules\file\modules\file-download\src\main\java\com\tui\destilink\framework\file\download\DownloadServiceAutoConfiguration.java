package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.download.impl.AtcomresDownloadServiceImpl;
import com.tui.destilink.framework.file.download.impl.DummyFileDownloadServiceFactory;
import com.tui.destilink.framework.file.download.impl.FileSystemFileDownloadServiceFactory;
import com.tui.destilink.framework.file.storage.StorageService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.util.List;

@RequiredArgsConstructor
@AutoConfiguration
public class DownloadServiceAutoConfiguration {

    @Bean
    AtcomresDownloadConfiguration atcomresDownloadConfiguration() {
        return new AtcomresDownloadConfiguration();
    }

    @Bean
    @ConditionalOnBean({DownloadConfiguration.class, StorageService.class})
    FileDownloadServiceFactory fileDownloadServiceFactory(DownloadConfiguration configuration,
                                                          StorageService storageService,
                                                          ResourcePatternResolver resourcePatternResolver,
                                                          List<AbstractFileDownloadServiceFactory> factories) {
        return new FileDownloadServiceFactory(configuration, storageService, resourcePatternResolver, factories);
    }

    @Bean
    @ConditionalOnBean(FileDownloadService.class)
    DownloadService downloadService(FileDownloadService fileDownloadService, DownloadConfiguration configuration) {
        return new DownloadService(fileDownloadService, configuration);
    }

    @Bean
    @ConditionalOnBean(DownloadService.class)
    AtcomresDownloadService atcomresDownloadService(AtcomresDownloadConfiguration atcomresDownloadConfiguration,
                                                    DownloadService downloadService,
                                                    DownloadConfiguration downloadConfiguration) {
        return new AtcomresDownloadServiceImpl(atcomresDownloadConfiguration, downloadService, downloadConfiguration);
    }

    @Bean
    AbstractFileDownloadServiceFactory dummyFileDownloadServiceFactory() {
        return new DummyFileDownloadServiceFactory();
    }

    @Bean
    AbstractFileDownloadServiceFactory fileSystemFileDownloadServiceFactory() {
        return new FileSystemFileDownloadServiceFactory();
    }

}
