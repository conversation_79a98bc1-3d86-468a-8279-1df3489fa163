package com.tui.destilink.framework.trips.core.logging;

import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class TripsContextTest {

    protected static final String BUSINESS_PROCESS_ID = UUID.randomUUID().toString();
    protected static final String BUSINESS_PROCESS_NAME = "businessProcessName";

    @Test
    void givenTripsJobContextShouldBeBuildCorrectly() {
        TripsJobContext jobContext = TripsJobContext.builder()
                .businessProcessId(BUSINESS_PROCESS_ID)
                .businessProcessName("businessProcessName")
                .build();

        assertThat(jobContext.getBusinessProcessId()).isEqualTo(BUSINESS_PROCESS_ID);
        assertThat(jobContext.getBusinessProcessName()).isEqualTo(BUSINESS_PROCESS_NAME);

        jobContext = jobContext.toBuilder()
                .businessProcessId(UUID.randomUUID().toString())
                .build();

        assertThat(jobContext.getBusinessProcessId()).isNotEqualTo(BUSINESS_PROCESS_ID);
        assertThat(jobContext.getBusinessProcessName()).isEqualTo(BUSINESS_PROCESS_NAME);
    }

    @Test
    void givenTripsEventContextShouldBeBuildCorrectly() {
        TripsEventContext eventContext = TripsEventContext.builder()
                .businessProcessId(BUSINESS_PROCESS_ID)
                .businessProcessName("businessProcessName")
                .eventId("eventId")
                .build();

        assertThat(eventContext.getBusinessProcessId()).isEqualTo(BUSINESS_PROCESS_ID);
        assertThat(eventContext.getBusinessProcessName()).isEqualTo(BUSINESS_PROCESS_NAME);
        assertThat(eventContext.getEventId()).isEqualTo("eventId");

        eventContext = eventContext.toBuilder()
                .businessProcessId(UUID.randomUUID().toString())
                .build();

        assertThat(eventContext.getBusinessProcessId()).isNotEqualTo(BUSINESS_PROCESS_ID);
        assertThat(eventContext.getBusinessProcessName()).isEqualTo(BUSINESS_PROCESS_NAME);
        assertThat(eventContext.getEventId()).isEqualTo("eventId");

        assertThat(TripsContextConstants.BUSINESS_PROCESS_ID).isEqualTo("trips.businessProcessId");
        MDC.put(TripsContextConstants.BUSINESS_PROCESS_ID, BUSINESS_PROCESS_ID);
        MDC.put(TripsContextConstants.BUSINESS_PROCESS_NAME, BUSINESS_PROCESS_NAME);
        MDC.put(TripsContextConstants.EVENT_ID, "eventId");
        MDC.put(TripsContextConstants.EVENT_VERSION, "1.0.0");
        MDC.put(TripsContextConstants.EVENT_TYPE, "type");
        MDC.put(TripsContextConstants.CREATOR, "creator");
        MDC.put(TripsContextConstants.PRODUCED_BY, "producedBy");
        MDC.put(TripsContextConstants.PRODUCED_BY_VERSION, "producedByVersion");
        MDC.put(TripsContextConstants.PRODUCED_TIME_STAMP, "producedTimestamp");
    }
}
