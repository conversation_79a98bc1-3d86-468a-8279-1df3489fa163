package com.tui.destilink.framework.trips.core.logging;

import lombok.EqualsAndHashCode;
import lombok.Value;
import lombok.experimental.SuperBuilder;

import java.time.ZonedDateTime;

@Value
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class TripsEventContext extends TripsJobContext {

    String type;
    String eventId;
    String eventType;
    String eventVersion;
    ZonedDateTime producedTimeStamp;

}
