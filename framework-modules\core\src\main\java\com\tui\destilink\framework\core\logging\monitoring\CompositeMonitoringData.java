package com.tui.destilink.framework.core.logging.monitoring;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import lombok.extern.jackson.Jacksonized;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.util.List;
import java.util.Objects;

@Builder
@Data
@Jacksonized
public class CompositeMonitoringData {

    private static final ObjectMapper OBJECT_MAPPER = Jackson2ObjectMapperBuilder.json().build();

    @Singular
    private final List<MonitoringData> values;

    @JsonValue
    JsonNode toMergedJson() {
        return values.stream()
                .map(OBJECT_MAPPER::valueToTree)
                .filter(Objects::nonNull)
                .filter(ObjectNode.class::isInstance)
                .map(ObjectNode.class::cast)
                .reduce(ObjectNode::setAll)
                .orElseGet(OBJECT_MAPPER::createObjectNode);
    }
}
