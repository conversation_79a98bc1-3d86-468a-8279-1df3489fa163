package com.tui.destilink.framework.cloudevents.it;

import lombok.Builder;
import lombok.Getter;

import java.util.UUID;

@Getter
@Builder
public class TestPojo2 {

    public static final TestPojo2 VALID_TEST_DATA = TestPojo2.builder()
            .helloWorld21("I am helloWorld2_1")
            .helloWorld22("I am helloWorld2_2")
            .build();

    private final String id = UUID.randomUUID().toString();
    private final String helloWorld21;
    private final String helloWorld22;
}
