openapi: 3.0.2
info:
  title: AUB models
  contact:
    name: TUI InfoTec GmbH
    url: http://www.tui-infotec.com
  version: "@drh-aub-oneschema.version@"

paths:
  /reservations:
    get:
      operationId: retrieve all entities
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object

components:
  schemas:
    ###### book #####
    BookRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: PreviewBookRequest according to preview url parameter true
      allOf:
        - $ref: "#/components/schemas/AbstractRequest"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/BookRequestReservationAction"

    BookResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: BookResponse, preview url parameter is mirrored in response audit header
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/BookResponseReservationAction"

    BookRequestReservationAction:
      description: A book request needs a successfull Prebook response at any time.  Depending on the time scale between a successfull Prebook response and the book request the price could be lower or higher. You have the possibility to send an accepted percentage tolerance between your prebook and the final purchase rate in a book response. If the price is higher than this tolerance level the DBH will cancel the booking and inform you about this automatically. If no tolerance is set in the request the DBH will use the standard parameter configuration for your setup.  Please get in contact with our support team (<EMAIL>) to get more detailed information especially for your own setup.
      allOf:
        - $ref: "#/components/schemas/AbstractRequestReservationAction"
        - type: object
          required:
            - bookingToken
            - paxDefinition
            - internalReferences
          properties:
            bookingToken:
              $ref: "#/components/schemas/BookingToken"
            purchaseRateTolerance:
              description: Percentage which defines a range of tolerance between the purchase rate of a prebook response and the associated book response. If no tolerance is set in the request the DBH will use the standard parameter configuration for your setup. (KGA398)
              type: number
              format: float
              example: 10.123
            reservationActionMetaData:
              $ref: "#/components/schemas/BookRequestReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/BookRequestPaxDefinition"
            internalReferences:
              $ref: "#/components/schemas/BookRequestInternalReferences"

    BookRequestReservationActionMetaData:
      description: metadata for booking request
      type: object
      required:
        - crossUnit
      properties:
        customerToSupplierMessages:
          type: array
          items:
            $ref: "#/components/schemas/SupplierMessageRequest"
        tuiToSupplierMessages:
          type: array
          items:
            $ref: "#/components/schemas/SupplierMessageRequest"
        crossUnit:
          $ref: "#/components/schemas/CrossUnit"

    BookRequestInternalReferences:
      type: object
      required:
        - nonAccoSourcingReservationReferences
      properties:
        accoSourcingReservationReferences:
          $ref: "#/components/schemas/BookRequestAccoSourcingReservationReferences"
        nonAccoSourcingReservationReferences:
          $ref: "#/components/schemas/NonAccoSourcingReservationReferences"

    BookRequestAccoSourcingReservationReferences:
      type: object
      properties:
        crossUnitBookingId:
          $ref: "#/components/schemas/CrossUnitBookingId"

    BookResponseReservationAction:
      description: result of a book request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - paxDefinition
            - bookingStatus
            - confirmationStatus
          properties:
            bookingTimestamp:
              description: |
                KGA364
                Be aware that this element is only available for successfully processed reservation actions. 
                Erroneously processed reservation actions won't contain a bookingTimestamp!
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/BookResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    BookResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "BookResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### cancel #####
    CancelResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: CancelResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/CancelResponseReservationAction"

    CancelResponseReservationAction:
      description: result of a cancel request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - bookingTimestamp
            - bookingStatus
            - confirmationStatus
          properties:
            cancellationTimestamp:
              description: |
                KGA375
                Be aware that this element is only available for successfully processed reservation actions. 
                Erroneously processed reservation actions may not contain a cancellationTimestamp!
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            modificationTimestamp:
              description: KGA374
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            bookingTimestamp:
              description: KGA364
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/CancelResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    CancelResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "CancelResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    CancelReference:
      description: Reference created by supplier in case of a cancellation (KGA371)
      type: object
      properties:
        supplierSubCancellationRef:
          description: Reference created by supplier in case of a cancellation (KGA371)
          type: string
          example: 87982982

    ###### correction #####
    CorrectionResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: CorrectionResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/CorrectionResponseReservationAction"

    CorrectionResponseReservationAction:
      description: result of a Correction request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - bookingTimestamp
            - bookingStatus
            - confirmationStatus
          properties:
            bookingTimestamp:
              description: KGA364
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            modificationTimestamp:
              description: |
                KGA374
                Be aware that this element is only available for successfully processed reservation actions. 
                Erroneously processed reservation actions may not contain a modificationTimestamp!
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/CorrectionResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    CorrectionResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "CorrectionResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### get reservation ids #####
    GetReservationIdsResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: GetReservationIdsResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/GetReservationIdsResponseReservationAction"

    GetReservationIdsResponseReservationAction:
      description: Get the matching reservation ids for this search request.
      allOf:
        - $ref: "#/components/schemas/AbstractReservationAction"
        - type: object
          required:
            - internalReferences
          properties:
            internalReferences:
              $ref: "#/components/schemas/InternalReferences"
            reservationActionMetaData:
              $ref: "#/components/schemas/GetReservationIdsResponseReservationActionMetaData"

    GetReservationIdsResponseReservationActionMetaData:
      type: object
      required:
        - externalReferences
      properties:
        externalReferences:
          $ref: "#/components/schemas/ExternalReferences"

    ###### get reservations #####
    GetReservationsResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: GetReservationsResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/GetReservationsResponseReservationAction"

    GetReservationsResponseReservationAction:
      description: result of a get reservations request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - bookingStatus
            - confirmationStatus
          properties:
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"
            bookingTimestamp:
              description: KGA364
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            modificationTimestamp:
              description: KGA374
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            cancellationTimestamp:
              description: KGA375
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/GetReservationsResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"

    GetReservationsResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "GetReservationsResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### modify #####
    ModifyRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: ModifyRequest
      allOf:
        - $ref: "#/components/schemas/AbstractRequest"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/ModifyRequestReservationAction"

    ModifyResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: ModifyResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/ModifyResponseReservationAction"

    ModifyRequestReservationAction:
      description: A MODIFY request needs all external reservation Ids which were returned in a successfull book response.
      allOf:
        - $ref: "#/components/schemas/AbstractUnitReferenceReservationAction"
        - type: object
          required:
            - bookingToken
            - reservationActionMetaData
            - internalReferences
          properties:
            bookingToken:
              $ref: "#/components/schemas/BookingToken"
            reservationActionMetaData:
              $ref: "#/components/schemas/ModifyRequestReservationActionMetaData"
            internalReferences:
              $ref: "#/components/schemas/InternalReferences"

    ModifyRequestReservationActionMetaData:
      description: metadata for modify booking request
      type: object
      required:
        - crossUnit
      properties:
        crossUnit:
          $ref: "#/components/schemas/CrossUnit"

    ModifyResponseReservationAction:
      description: result of a modify request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - bookingTimestamp
            - paxDefinition
            - bookingStatus
            - confirmationStatus
          properties:
            bookingTimestamp:
              description: KGA364
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            modificationTimestamp:
              description: |
                KGA374
                Be aware that this element is only available for successfully processed reservation actions. 
                Erroneously processed reservation actions may not contain a modificationTimestamp!
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/ModifyResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    ModifyResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "ModifyResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### prebook #####
    PreviewBookRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: PreviewBookRequest according to preview url parameter true
      allOf:
        - $ref: "#/components/schemas/AbstractRequest"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/PreviewBookRequestReservationAction"

    PreviewBookResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: BookResponse for BOOK/PREBOOK, preview url parameter is mirrored in response audit header
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/PreviewBookResponseReservationAction"

    PreviewBookRequestReservationAction:
      description: A preview book request is needed prior a real booking request to obtain a booking token and ensure that the desired reservation can be done.
      allOf:
        - $ref: "#/components/schemas/AbstractUnitReferenceReservationAction"
        - type: object
          required:
            - unitReference
            - stayDateBand
            - nights
            - paxDefinition
            - reservationActionMetaData
          properties:
            reservationActionMetaData:
              $ref: "#/components/schemas/PreviewBookRequestReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/PreviewBookRequestPaxDefinition"

    PreviewBookRequestPaxDefinition:
      description: pax definition for booking request
      type: object
      required:
        - paxDetails
        - numberOfPax
      properties:
        paxDetails:
          type: array
          items:
            $ref: "#/components/schemas/RequestPax"
          minItems: 1
        numberOfPax:
          description: KGA177
          type: integer
          format: int32
          minimum: 1
          example: 2

    PreviewBookRequestReservationActionMetaData:
      description: metadata for preview preview booking request
      type: object
      required:
        - crossUnit
        - onRequestStatusAllow
      properties:
        crossUnit:
          $ref: "#/components/schemas/CrossUnit"
        onRequestStatusAllow:
          $ref: "#/components/schemas/OnRequestStatusAllow"

    PreviewBookResponseReservationAction:
      description: result of a pre-book request
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationAction"
        - type: object
          required:
            - paxDefinition
            - bookingStatus
            - confirmationStatus
          properties:
            bookingToken:
              $ref: "#/components/schemas/BookingToken"
            reservationActionMetaData:
              $ref: "#/components/schemas/PreviewBookResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    PreviewBookResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "PreviewBookResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - supplierOfferDetails
            - onRequestStatusAllow
          properties:
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            onRequestStatusAllow:
              $ref: "#/components/schemas/OnRequestStatusAllow"

    ###### premodify #####
    PreviewModifyRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: PreviewModifyRequest
      allOf:
        - $ref: "#/components/schemas/AbstractRequest"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/PreviewModifyRequestReservationAction"

    PreviewModifyResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: PreviewModifyResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/PreviewModifyResponseReservationAction"

    PreviewModifyRequestReservationAction:
      description: A preview MODIFY request needs all external reservation Ids which were returned in a successfull book response.
      allOf:
        - $ref: "#/components/schemas/AbstractUnitReferenceReservationAction"
        - type: object
          required:
            - unitReference
            - stayDateBand
            - nights
            - reservationActionMetaData
            - internalReferences
          properties:
            reservationActionMetaData:
              $ref: "#/components/schemas/PreviewModifyRequestReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ModifyRequestPaxDefinition"
            internalReferences:
              $ref: "#/components/schemas/InternalReferences"

    PreviewModifyRequestReservationActionMetaData:
      description: metadata for preview modify booking request
      type: object
      properties:
        externalReferences:
          $ref: "#/components/schemas/PreviewModifyRequestExternalReferences"
        customerToSupplierMessages:
          type: array
          items:
            $ref: "#/components/schemas/SupplierMessageRequest"
        tuiToSupplierMessages:
          type: array
          items:
            $ref: "#/components/schemas/SupplierMessageRequest"
        crossUnit:
          $ref: "#/components/schemas/CrossUnit"

    PreviewModifyRequestExternalReferences:
      type: object
      properties:
        externalTUIRef:
          description: Reference for Supplier extranets created by DBH based on TRIPS reference (KGA367)
          type: string
          example: 0002;12345678
        supplierMainBookingRef:
          description: Main Reference created by Supplier in case of a booking (KGA369)
          type: string
          example: GHH6FHFG

    PreviewModifyResponseReservationAction:
      description: result of a preview modify request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - paxDefinition
            - bookingStatus
            - confirmationStatus
          properties:
            bookingToken:
              $ref: "#/components/schemas/BookingToken"
            reservationActionMetaData:
              $ref: "#/components/schemas/PreviewModifyResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    PreviewModifyResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "PreviewModifyResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - supplierOfferDetails
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### recosting #####
    RecostingRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: RecostingRequest
      allOf:
        - $ref: "#/components/schemas/AbstractRecostingRequest"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/RecostingRequestReservationAction"

    AbstractRecostingRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true, builderMethodName = "baseBuilder")'
      type: object
      required:
        - type
      properties:
        type:
          description: request type
          type: string
      discriminator:
        propertyName: "type"

    RecostingResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: RecostingResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          properties:
            reservationActions:
              type: array
              items:
                $ref: "#/components/schemas/RecostingResponseReservationAction"

    RecostingRequestReservationAction:
      description: A Recosting request needs all external reservation Ids which were returned in a successful book response.
      allOf:
        - $ref: "#/components/schemas/AbstractRequestReservationAction"
        - type: object
          required:
            - reservationActionMetaData
            - internalReferences
          properties:
            reservationActionMetaData:
              $ref: "#/components/schemas/RecostingRequestReservationActionMetaData"
            internalReferences:
              $ref: "#/components/schemas/RecostingRequestInternalReferences"

    RecostingRequestInternalReferences:
      type: object
      properties:
        accoSourcingReservationReferences:
          $ref: "#/components/schemas/AccoSourcingReservationReferences"

    RecostingRequestReservationActionMetaData:
      description: metadata for Recosting booking request
      type: object
      required:
        - crossUnit
        - supplierOfferDetails
      properties:
        crossUnit:
          $ref: "#/components/schemas/CrossUnit"
        supplierOfferDetails:
          $ref: "#/components/schemas/RecostingRequestSupplierOfferDetails"

    RecostingRequestSupplierOfferDetails:
      description: KGA379
      type: object
      properties:
        supplierReservationKeys:
          $ref: "#/components/schemas/SupplierReservationKeys"

    RecostingResponseReservationAction:
      description: result of a Recosting request
      allOf:
        - $ref: "#/components/schemas/AbstractDubhIdResponseReservationAction"
        - type: object
          required:
            - bookingTimestamp
            - bookingStatus
            - confirmationStatus
          properties:
            bookingTimestamp:
              description: KGA364
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            modificationTimestamp:
              description: |
                KGA374
                Be aware that this element is only available for successfully processed reservation actions. 
                Erroneously processed reservation actions may not contain a modificationTimestamp!
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            reservationActionMetaData:
              $ref: "#/components/schemas/RecostingResponseReservationActionMetaData"
            paxDefinition:
              $ref: "#/components/schemas/ResponsePaxDefinition"
            bookingStatus:
              $ref: "#/components/schemas/BookingStatus"
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"

    RecostingResponseReservationActionMetaData:
      x-class-extra-annotation: '@io.swagger.v3.oas.annotations.media.Schema(name = "RecostingResponseReservationActionMetaData")'
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationActionMetaData"
        - type: object
          required:
            - externalReferences
            - virtualCreditCard
          properties:
            externalReferences:
              $ref: "#/components/schemas/ExternalReferences"
            customerToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            tuiToSupplierMessages:
              type: array
              items:
                $ref: "#/components/schemas/SupplierMessageResponse"
            supplierOfferDetails:
              $ref: "#/components/schemas/SupplierOfferDetails"
            virtualCreditCard:
              $ref: "#/components/schemas/VirtualCreditCards"

    ###### release #####
    ReleaseResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true)'
      description: ReleaseResponse
      allOf:
        - $ref: "#/components/schemas/AbstractResponse"
        - type: object
          required:
            - reservationActions
          properties:
            reservationActions:
              x-field-extra-annotation: '@lombok.Builder.Default'
              type: array
              items:
                $ref: "#/components/schemas/ReleaseResponseReservationAction"

    ReleaseResponseReservationAction:
      description: result of a release response
      allOf:
        - $ref: "#/components/schemas/AbstractRequestReservationAction"
        - type: object
          properties:
            bookingToken:
              $ref: "#/components/schemas/BookingToken"

    ###### common section #####
    Accommodation:
      description: KGA88
      type: object
      required:
        - id
      properties:
        id:
          description: CKGA1
          type: string
          example: 34332LPA
        version:
          description: CKGA3
          type: string
          example: 5.0.1 OR 2021-07-20T07:30:27.599038Z

    Amount:
      title: "Amount"
      description: "amount of money spent on building or producing an object (KGA129). Compared to MonetaryAmount does not specify the corresponding currency here."
      type: number
      format: float
      example: 10.15

    AbstractAnnotation:
      required:
        - type
      properties:
        type:
          description: annotation type
          type: string
      discriminator:
        propertyName: "type"

    Annotation:
      allOf:
        - $ref: "#/components/schemas/AbstractAnnotation"
        - type: object
          description: KGA59
          required:
            - reservationActionInternalKeyReferences
          properties:
            reservationActionInternalKeyReferences:
              description: internalKey of reservation action that this annotation is meant for - if multiple actions have the same information this can be a list to avoid redundancy
              type: array
              items:
                type: string
            tuiInternalInformation:
              description: Includes text information from the supplier for TUI. Do not forward this information to an end customer at any time (!) (KGA393)
              type: array
              items:
                type: string
            customerInformation:
              description: Includes text information from the supplier for a customer. We suggest to forward this information to an end customer and/or the sales system at any time. (KGA394)
              type: array
              items:
                type: string

    CrossUnitAnnotation:
      allOf:
        - $ref: "#/components/schemas/AbstractAnnotation"
        - type: object
          required:
            - reservationActionInternalKeyReferences
          properties:
            reservationActionInternalKeyReferences:
              description: internalKey of reservation action that this annotation is meant for - if multiple actions have the same information this can be a list to avoid redundancy
              type: array
              items:
                type: string

    AllocationAvailability:
      description: KGA260
      type: object
      required:
        - available
      properties:
        max:
          description: KGA380
          type: integer
          format: int32
          minimum: 0
          example: 3
        quantity:
          description: KGA176
          type: integer
          format: int32
          minimum: 0
          example: 454
        available:
          description: "defines whether the given quanity can be considered to be available (KGA261)"
          type: boolean
          example: true

    BookRequestPaxDefinition:
      description: pax definition for booking request
      type: object
      required:
        - paxDetails
        - numberOfPax
      properties:
        paxDetails:
          type: array
          items:
            $ref: "#/components/schemas/BookRequestPax"
          minItems: 1
        numberOfPax:
          description: KGA177
          type: integer
          format: int32
          minimum: 1
          example: 2

    BookRequestPax:
      allOf:
        - $ref: "#/components/schemas/PaxBasic"
        - type: object
          required:
            - familyName
            - givenName
          properties:
            nameTitle:
              description: KGA389
              type: string
              minLength: 1
              example: Commander
            familyName:
              description: KGA390
              type: string
              minLength: 1
              example: Sisko
            givenName:
              description: KGA391
              type: string
              minLength: 1
              example: Benajmin-Lafajet

    BookingStatus:
      description: Possible values OPT, BKG, CNX (KGA360)
      enum:
        - OPT
        - BKG
        - CNX
      example: BKG

    BookingToken:
      description: |
        Content of this element is the value from the associated prebook/premodify response (KGA362).
        Be aware that this element is only available for successfully processed reservation actions. 
        Erroneously processed reservation actions won't contain a bookingToken!
      type: string
      minLength: 1
      example: "1254785478HGSHGG54874552145588#SUN"

    ConfirmationStatus:
      description: Possible values CON, DEN, HIS, REQ, EST (KGA361)
      enum:
        - CON
        - DEN
        - HIS
        - REQ
        - EST
      example: CON

    Contract:
      description: KGA107
      type: object
      required:
        - code
      properties:
        version:
          description: CKGA3
          type: string
          example: 1.0
        code:
          description: CKGA2
          type: string
          example: LOL1526

    Currency:
      title: "Currency"
      description: "generally accepted medium of exchange for goods or services. Describes a reference to a DestiOpenData currency object by using its id. (KGA65)"
      type: string
      minLength: 1
      example: EUR

    DestilinkBookHub:
      description: KGA352
      type: object
      required:
        - systemEnvironment
        - buildId
        - multiProxyInvocationId
        - group
      properties:
        systemEnvironment:
          description: KGA353
          type: string
          example: AWS_FRA_01
        buildId:
          description: KGA354
          type: string
          example: 2.5.4
        multiProxyInvocationId:
          description: KGA353
          type: string
          example: 45454545H3547456
        group:
          type: array
          items:
            $ref: "#/components/schemas/Group"

    ExternalBoardtype:
      description: KGA386
      type: object
      required:
        - key
      properties:
        key:
          description: CKGA21
          type: string
          minLength: 1
          example: BB2
        name:
          description: KGA10
          type: string
          minLength: 1
          example: Breakfast
        subKey:
          description: CKGA22
          type: string
          minLength: 1
          example: MB22
        subName:
          description: KGA397
          type: string
          minLength: 1
          example: CONTINENTAL
        additionalKey:
          type: string
          example: BB2

    ExternalRoom:
      description: KGA385
      type: object
      required:
        - key
      properties:
        key:
          description: CKGA21
          type: string
          minLength: 1
          example: DOUBEL
        name:
          description: KGA10
          type: string
          minLength: 1
          example: DOUBLEROOM
        subKey:
          description: CKGA22
          type: string
          minLength: 1
          example: HSHS727
        subName:
          description: KGA397
          type: string
          minLength: 1
          example: SIEVIEW_KINGSIZE
        additionalKey:
          type: string
          example: BB2

    ExternalReferences:
      description: all external reservation Ids which were returned in a successfull book response
      type: object
      required:
        - supplierReservationReferences
      properties:
        supplierReservationReferences:
          $ref: "#/components/schemas/SupplierReservationReferences"
        virtualCreditCardReferences:
          type: array
          items:
            $ref: "#/components/schemas/VirtualCreditCardReference"

    SupplierReservationReferences:
      type: object
      required:
        - externalTUIRef
      properties:
        externalTUIRef:
          description: Reference for Supplier extranets created by DBH based on TRIPS reference (KGA367)
          type: string
          example: 0002;12345678
        supplierMainBookingRef:
          description: |
            Main Reference created by Supplier in case of a booking (KGA369)
            Be aware that this element is only available for successfully processed reservation actions. 
            Erroneously processed reservation actions may not contain a supplierMainBookingRef!
          type: string
          example: GHH6FHFG
        supplierSubBookingRef:
          description: Sub Reference created by Supplier in case of a booking (KGA370)
          type: string
          example: 62929101H
        supplierCancelRef:
          description: Reference created by Supplier in case of a cancellation
          type: string
          example: 62929101H

    VirtualCreditCardReference:
      type: object
      required:
        - virtualCreditCardAnnotation
        - virtualCreditCardMainRef
        - virtualCreditCardSupplierSubRef
      properties:
        annotations:
          $ref: "#/components/schemas/VirtualCreditCardAnnotation"
        virtualCreditCardMainRef:
          type: string
        virtualCreditCardSupplierMainRef:
          type: string
        virtualCreditCardSupplierSubRef:
          type: string

    VirtualCreditCardAnnotation:
      allOf:
        - $ref: "#/components/schemas/AbstractAnnotation"
        - type: object
          description: KGA59
          properties:
            internalCardKey:
              type: string
              description: CKGA17, CKGA21
              example: ABC64839-JJ8

    Gender:
      description: Possible values M, F, D (KGA388)
      type: string
      enum:
        - M
        - F
        - D
      example: M

    Group:
      description: KGA283
      type: object
      required:
        - id
        - proxyInvocationId
        - groupContext
      properties:
        id:
          description: CKGA1
          type: string
          example: 1
        proxyInvocationId:
          description: KGA357
          type: string
          example: 2454547845452154
        groupContext:
          description: KGA358
          type: string
          example: TUI_CE_STANDARD

    MonetaryAmount:
      title: "Monetary Amount"
      description: "amount and currency. Amount of money spent on building or producing an object including its currency (KGA284)"
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: "#/components/schemas/Amount"
        currency:
          $ref: "#/components/schemas/Currency"

    PaxBasic:
      description: KGA387
      type: object
      required:
        - id
      properties:
        id:
          description: CKGA1
          type: string
          minLength: 1
          example: 1

    RequestPax:
      description: KGA387
      allOf:
        - $ref: "#/components/schemas/PaxBasic"
        - type: object
          required:
            - gender
          properties:
            gender:
              $ref: "#/components/schemas/Gender"
            age:
              description: KGA99
              type: integer
              format: int32
              minimum: 0
              example: 8
            nationality:
              description: KGA392
              type: string
              minLength: 1
              example: DEU
            birth:
              description: Birth day of the person. (optional)
              type: string
              format: date
              example: "2010-05-01"

    PaxAgeType:
      description: Possible values INF, CHD, ADU (KGA110)
      type: string
      enum:
        - INF
        - CHD
        - ADU
      example: INF

    SupplierReservationKeys:
      description: KGA382
      type: object
      properties:
        externalContractKey:
          description: KGA383
          type: string
          minLength: 1
          example: 1254HAH
        externalRoom:
          $ref: "#/components/schemas/ExternalRoom"
        externalBoardtype:
          $ref: "#/components/schemas/ExternalBoardtype"
        externalProduct:
          $ref: "#/components/schemas/ExternalProduct"
        externalRatePlan:
          $ref: "#/components/schemas/ExternalRatePlan"

    ExternalProduct:
      type: object
      required:
        - key
      properties:
        key:
          description: CKGA21
          type: string
          example: JP978400
        name:
          description: KGA10
          type: string
          example: Iberostar Cala Domingos
        additionalKey:
          type: string
          example: JP978400

    ExternalRatePlan:
      type: object
      properties:
        key:
          description: CKGA21
          type: string
          example: JP978400
        name:
          description: KGA10
          type: string
          example: Iberostar Cala Domingos
        additionalKey:
          type: string
          example: JP978400

    RequestAuditMetaData:
      type: object
      description: KGA348
      required:
        - tuiCompany
      properties:
        tuiCompany:
          $ref: "#/components/schemas/TuiCompany"

    ResponseAuditMetaData:
      allOf:
        - $ref: "#/components/schemas/AbstractAuditMetaData"
        - type: object
          description: KGA348
          required:
            - apiVersion
            - processTimeInMillis
          properties:
            apiVersion:
              description: server api version (CKGA3)
              type: string
              example: 1.0.0
            processTimeInMillis:
              description: KGA351
              type: integer
              format: int64
              minimum: 0
              example: 34
            requestPreviewParam:
              description: "value of the requests URL parameter: preview. TODO - KGA396?"
              type: boolean
            requestContentTypeHeader:
              description: "Content-type http-header from request"
              type: string
              example: application/vnd.destilink.dubh.api.v1+json
            requestAcceptLanguageHeader:
              description: "Accept-Language http-header from request"
              type: string
              example: de
            requestType:
              $ref: "#/components/schemas/RequestType"
            destilinkBookHub:
              $ref: "#/components/schemas/DestilinkBookHub"
            tuiCompany:
              $ref: "#/components/schemas/TuiCompany"

    RequestType:
      description: Business type of Request
      enum:
        - PREBOOK
        - BOOK
        - PREMODIFY
        - MODIFY
        - PRECANCEL
        - CANCEL
        - LOOKUP
        - LIST
        - ISSUE_LIST
        - ISSUE_LOOKUP
        - SUPPLIERS_LIST
        - SUPPLIER_DETAIL
        - RECOSTING
        - CORRECTION
        - RELEASE

    ResponsePaxDefinition:
      description: KGA387
      type: object
      required:
        - paxDetails
        - numberOfPax
      properties:
        paxDetails:
          type: array
          items:
            $ref: "#/components/schemas/ResponsePax"
          minItems: 1
        numberOfPax:
          description: KGA177
          type: integer
          format: int32
          minimum: 1
          example: 2

    ModifyRequestPaxDefinition:
      description: pax definition for modification request
      type: object
      required:
        - paxDetails
        - numberOfPax
      properties:
        paxDetails:
          type: array
          items:
            $ref: "#/components/schemas/ModifyRequestPax"
          minItems: 1
        numberOfPax:
          description: KGA177
          type: integer
          format: int32
          minimum: 1
          example: 2

    ModifyRequestPax:
      allOf:
        - $ref: "#/components/schemas/RequestPax"
        - type: object
          required:
            - familyName
            - givenName
          properties:
            nameTitle:
              description: KGA389
              type: string
              minLength: 1
              example: Commander
            familyName:
              description: KGA390
              type: string
              minLength: 1
              example: Sisko
            givenName:
              description: KGA391
              type: string
              minLength: 1
              example: Benajmin-Lafajet

    ResponsePax:
      description: KGA387
      allOf:
        - $ref: "#/components/schemas/PaxBasic"
        - type: object
          required:
            - id
            - confirmationStatus
          properties:
            id:
              description: CKGA1
              type: string
              minLength: 1
              example: 1
            confirmationStatus:
              $ref: "#/components/schemas/ConfirmationStatus"
            gender:
              $ref: "#/components/schemas/Gender"
            age:
              description: KGA99
              type: integer
              format: int32
              minimum: 0
              example: 8
            nationality:
              description: KGA392
              type: string
              minLength: 1
              example: DEU
            birth:
              description: Birth day of the person. (optional)
              type: string
              format: date
              example: "2010-05-01"
            paxAgeType:
              $ref: "#/components/schemas/PaxAgeType"
            nameTitle:
              description: KGA389
              type: string
              minLength: 1
              example: Commander
            familyName:
              description: KGA390
              type: string
              minLength: 1
              example: Sisko
            givenName:
              description: KGA391
              type: string
              minLength: 1
              example: Benajmin-Lafajet

    RoomType:
      description: KGA105
      type: object
      required:
        - id
      properties:
        id:
          description: CKGA1
          type: string
          example: uuuwhsop9839939
        version:
          description: CKGA3
          type: string
          example: uuuwhsop9839939
        additionalId:
          type: string

    BoardType:
      description: KGA101
      type: object
      required:
        - id
      properties:
        id:
          description: CKGA1
          type: string
          example: 1d8d0856-0b4e-4b4a-aa28-63266e963050
        version:
          description: CKGA3
          type: string
          example: 5.0.1 OR 2021-07-20T07:30:27.599038Z
        additionalId:
          type: string
          example: "HB:HB:BB"

    SupplierOfferDetails:
      description: KGA379
      type: object
      properties:
        allocationAvailability:
          $ref: "#/components/schemas/AllocationAvailability"
        supplierReservationKeys:
          $ref: "#/components/schemas/SupplierReservationKeys"

    AbstractSupplierPriceElement:
      required:
        - type
      properties:
        type:
          description: supplier price element type
          type: string
      discriminator:
        propertyName: "type"

    SupplierPriceElement:
      allOf:
        - $ref: "#/components/schemas/AbstractSupplierPriceElement"
        - type: object
          description: KGA308
          required:
            - purchaseRate
          properties:
            priceBeforeTax:
              $ref: "#/components/schemas/InDetailParent"
            purchaseRate:
              $ref: "#/components/schemas/InDetailParent"
            salesOfferDetails:
              type: array
              items:
                $ref: "#/components/schemas/SalesOfferDetail"
            fee:
              $ref: "#/components/schemas/AllFees"
            costTimestamp:
              description: KGA115
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"
            calculationTimestamp:
              description: KGA115
              type: string
              format: date-time
              example: "2018-06-13T14:13:00Z"

    AllFees:
      description: KGA308
      type: object
      properties:
        modificationFee:
          $ref: "#/components/schemas/MonetaryAmount"
        cancellationFee:
          $ref: "#/components/schemas/MonetaryAmount"

    SalesOfferDetail:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        offerType:
          type: string
        distributorCodes:
          type: array
          items:
            type: string
        onSaleOffer:
          type: boolean

    InDetailParent:
      allOf:
        - $ref: "#/components/schemas/MonetaryAmount"
        - type: object
          properties:
            inDetail:
              $ref: "#/components/schemas/InDetailChild"

    InDetailChild:
      type: object
      properties:
        perDate:
          type: array
          items:
            $ref: "#/components/schemas/InDetailPerDate"
        perPax:
          type: array
          items:
            $ref: "#/components/schemas/InDetailPerPax"

    InDetailPerDate:
      type: object
      properties:
        date:
          type: string
          format: date
          example: "2020-05-01"
        amount:
          $ref: "#/components/schemas/Amount"
        detailedPriceInformation:
          type: array
          items:
            $ref: "#/components/schemas/DetailedPriceInformation"

    DetailedPriceInformation:
      type: object
      properties:
        amount:
          $ref: "#/components/schemas/Amount"
        paxId:
          type: string
        inDetail:
          $ref: "#/components/schemas/InDetailPriceInformation"

    InDetailPriceInformation:
      type: object
      properties:
        rate:
          $ref: "#/components/schemas/Amount"
        board:
          $ref: "#/components/schemas/Amount"
        taxes:
          type: array
          items:
            $ref: "#/components/schemas/InDetailField"
        fees:
          type: array
          items:
            $ref: "#/components/schemas/InDetailField"
        supplements:
          type: array
          items:
            $ref: "#/components/schemas/InDetailField"
        salesOfferIds:
          type: array
          items:
            $ref: "#/components/schemas/InDetailField"

    InDetailField:
      type: object
      properties:
        id:
          type: string
        amount:
          $ref: "#/components/schemas/Amount"

    InDetailPerPax:
      type: object
      properties:
        id:
          type: string
        amount:
          $ref: "#/components/schemas/Amount"
        salesOfferIds:
          type: array
          items:
            type: string

    StayDateBand:
      description: KGA116
      type: object
      required:
        - startDate
        - endDate
        - name
      properties:
        startDate:
          description: "point in time, when something starts/started (KGA116)"
          type: string
          format: date
          example: "2020-05-01"
        endDate:
          description: "point in time, when something ended or is supposed to end (KGA117)"
          type: string
          format: date
          example: "2020-05-06"
        name:
          description: KGA120
          type: string
          example: TravelDate

    TuiCompany:
      description: Internal TUI Company who wants to book (KGA210)
      type: object
      required:
        - distributor
        - traceCode
      properties:
        distributor:
          description: Type of the compancy (KGA121)
          type: string
          example: DIST
        traceCode:
          description: Unique Id to identify the company by group finance
          type: string
          example: 00002
        legalEntity:
          type: string
        brand:
          description: brand code of related company
          type: string
          example: TUI_NL
        sourceMarket:
          description: code to identify the related sourcemarket
          type: string
          example: BE

    RequestedUnitComponents:
      description: KGA279
      type: object
      required:
        - accommodation
        - contract
        - roomType
        - boardType
      properties:
        accommodation:
          $ref: "#/components/schemas/Accommodation"
        contract:
          $ref: "#/components/schemas/Contract"
        roomType:
          $ref: "#/components/schemas/RoomType"
        boardType:
          $ref: "#/components/schemas/BoardType"

    CrossUnit:
      type: object
      required:
        - isCrossUnitBooking
      properties:
        isCrossUnitBooking:
          description: Creates a link between units in same request for example in case of Unit extra (HolidayParks)
          type: boolean
        annotations:
          $ref: "#/components/schemas/CrossUnitAnnotation"

    SupplierDetails:
      type: object
      required:
        - supplier
        - subSupplier
        - supplierId
        - isDynamic
      properties:
        supplier:
          description: SupplierCode from UnitHUB
          type: string
        subSupplier:
          description: SubSupplierCode from UnitHUB
          type: string
        supplierId:
          description: FinancialSupplierId from UnitHUB
          type: string
        isDynamic:
          description: Identify Contracted or dynamic Supplier (Perspective TUI)
          type: boolean

    UnitReference:
      description: key elements to identify the referenced unit
      type: object
      required:
        - id
        - version
      properties:
        id:
          description: CKGA1
          type: string
          example: 127980299021
        version:
          description: The version of the UnitId during the (pre)booking request (CKGA1)
          type: string
          example: 5.0.1
        requestedUnitComponents:
          $ref: "#/components/schemas/RequestedUnitComponents"

    IssueItem:
      description: KGA395
      type: object
      required:
        - code
        - descriptions
      properties:
        code:
          description: CKGA2
          type: string
          example: DBH_VALIDATE-I4
        descriptions:
          type: array
          items:
            $ref: "#/components/schemas/IssueItemDescription"

    IssueItemDescription:
      type: object
      required:
        - language
        - "description"
      properties:
        language:
          type: string
        "description":
          description: KGA395
          type: string

    IssueAnnotation:
      allOf:
        - $ref: "#/components/schemas/AbstractAnnotation"
        - type: object
          required:
            - reservationActionInternalKeyReferences
          properties:
            reservationActionInternalKeyReferences:
              description: internalKey of reservation action that this annotation is meant for - if multiple actions have the same information this can be a list to avoid redundancy
              type: array
              items:
                type: string
      discriminator:
        propertyName: "type"

    SupplierMessageRequest:
      type: object
      description: Message to be forwarded to supplier
      properties:
        id:
          type: string
          description: Message identifier
        category:
          type: string
          description: Message category
        text:
          type: string
          description: Message text to be forwarded to supplier
        status:
          type: string

    SupplierMessageResponse:
      allOf:
        - $ref: "#/components/schemas/SupplierMessageRequest"
        - type: object
          description: Meassge forwarded to supplier
          properties:
            forwarded:
              type: boolean
              description: Forwarded to supplier

    TraceIds:
      description: Group to identify and store trips standard information during reservation process
      type: object
      required:
        - businessProcessId
        - eventId
      properties:
        businessProcessId:
          description: Main "Trace" for TRIPS to identify a the data flow in E2E perspective
          type: string
          format: uuid
          example: "92738a74-dd1d-487d-8c32-428c0578abea"
        businessProcessName:
          description: Process name for used businessProcessId
          type: string
          example: "TUI BE Retail AMEND"
        creator:
          description: Creator of the request against BookHUB like SNS2REST
          type: string
          example: "DestiLink_UnitBookHUB"
        eventId:
          description: ID for the specific SO-Event stream
          type: string
          example: "a186bfc0-0cd8-4db3-bb5f-d54d389ae535"

    # ############################################################################################################
    # ###########         ABSTRACTS       ########################################################################
    # ############################################################################################################

    AbstractAuditMetaData:
      type: object
      description: KGA348
      required:
        - timestamp
      properties:
        traceIds:
          $ref: "#/components/schemas/TraceIds"
        timestamp:
          description: KGA115
          type: string
          format: date-time
          example: "2018-06-13T14:13:00Z"

    AbstractDubhIdResponseReservationAction:
      description: base class having an internal reservation action id
      allOf:
        - $ref: "#/components/schemas/AbstractResponseReservationAction"
        - type: object
          required:
            - internalReferences
          properties:
            internalReferences:
              $ref: "#/components/schemas/InternalReferences"
      discriminator:
        propertyName: "type"

    AbstractRequest:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true, builderMethodName = "baseBuilder")'
      description: common request elements
      type: object
      required:
        - type
        - auditMetaData
      properties:
        type:
          description: request type
          type: string
        auditMetaData:
          $ref: "#/components/schemas/RequestAuditMetaData"
      discriminator:
        propertyName: "type"

    AbstractRequestReservationAction:
      description: abstract base class for all request bodies with reservation actions
      allOf:
        - $ref: "#/components/schemas/AbstractReservationAction"
        - type: object
      discriminator:
        propertyName: "type"

    AbstractUnitReferenceReservationAction:
      description: abstract base class for all request bodies with reservation actions with unit references
      allOf:
        - $ref: "#/components/schemas/AbstractRequestReservationAction"
        - type: object
          properties:
            unitReference:
              $ref: "#/components/schemas/UnitReference"
            stayDateBand:
              $ref: "#/components/schemas/StayDateBand"
            nights:
              description: KGA148
              type: integer
              format: int32
              minimum: 0
              example: 5
      discriminator:
        propertyName: "type"

    AbstractReservationAction:
      description: KGA377
      type: object
      required:
        - type
        - internalKey
      properties:
        type:
          description: action type
          type: string
        internalKey:
          description: unique internal key for reservation action
          type: string
      discriminator:
        propertyName: "type"

    AbstractReservationActionMetaData:
      description: KGA365
      type: object
      required:
        - supplierPriceElements
      properties:
        supplierPriceElements:
          $ref: "#/components/schemas/SupplierPriceElement"
      discriminator:
        propertyName: "type"

    AbstractResponse:
      x-class-extra-annotation: '@lombok.experimental.SuperBuilder(toBuilder = true, builderMethodName = "baseBuilder")'
      type: object
      required:
        - type
        - auditMetaData
      properties:
        type:
          description: response type
          type: string
        auditMetaData:
          $ref: "#/components/schemas/ResponseAuditMetaData"
        issues:
          $ref: "#/components/schemas/Issues"
        errata:
          $ref: "#/components/schemas/Errata"
      discriminator:
        propertyName: "type"

    # ############################################################################################################
    # ###########         OBJECTS       ########################################################################
    # ############################################################################################################

    InternalReferences:
      type: object
      properties:
        accoSourcingReservationReferences:
          $ref: "#/components/schemas/AccoSourcingReservationReferences"
        nonAccoSourcingReservationReferences:
          $ref: "#/components/schemas/NonAccoSourcingReservationReferences"

    AccoSourcingReservationReferences:
      type: object
      required:
        - dubhId
      properties:
        crossUnitBookingId:
          $ref: "#/components/schemas/CrossUnitBookingId"
        dubhId:
          description: reservation id for use in this api (CKGA1)
          type: string
          example: 127980299021

    NonAccoSourcingReservationReferences:
      type: object
      required:
        - tuiSalesBookingId
      properties:
        tuiSalesBookingId:
          $ref: "#/components/schemas/TuiSalesBookingId"
        tuiSalesGroupBookingId:
          $ref: "#/components/schemas/TuiSalesGroupBookingId"

    TuiSalesBookingId:
      description: CKGA11
      type: string
      example: 4711_42HDGTHS

    TuiSalesGroupBookingId:
      type: string
      example: 4711_42HDGTHS

    CrossUnitBookingId:
      type: string
      example: SPNR352829HS

    Errata:
      type: object
      required:
        - annotations
      properties:
        annotations:
          type: array
          items:
            $ref: "#/components/schemas/Annotation"

    Issues:
      type: object
      required:
        - annotations
      properties:
        annotations:
          type: array
          items:
            $ref: "#/components/schemas/Issue"

    Issue:
      allOf:
        - $ref: "#/components/schemas/IssueAnnotation"
        - type: object
          description: A group which collect all warning and error codes which were created during processing of initial request.
          properties:
            warningCodes:
              $ref: "#/components/schemas/WarningCodes"
            errorCodes:
              $ref: "#/components/schemas/ErrorCodes"

    IssueItemList:
      type: object
      required:
        - totalCount
      properties:
        totalCount:
          type: integer

    WarningCodes:
      allOf:
        - $ref: "#/components/schemas/IssueItemList"
        - type: object
          required:
            - warnings
          properties:
            warnings:
              type: array
              items:
                $ref: "#/components/schemas/IssueItem"

    ErrorCodes:
      allOf:
        - $ref: "#/components/schemas/IssueItemList"
        - type: object
          required:
            - errors
          properties:
            errors:
              type: array
              items:
                $ref: "#/components/schemas/IssueItem"

    AbstractResponseReservationAction:
      description: abstract base class for all response bodies with reservation actions
      required:
        - unitReference
        - stayDateBand
        - nights
      allOf:
        - $ref: "#/components/schemas/AbstractUnitReferenceReservationAction"
        - type: object
      discriminator:
        propertyName: "type"

    AbstractResponseReservationActionMetaData:
      description: KGA365
      allOf:
        - $ref: "#/components/schemas/AbstractReservationActionMetaData"
        - type: object
          required:
            - supplierDetails
            - crossUnit
          properties:
            crossUnit:
              $ref: "#/components/schemas/CrossUnit"
            supplierDetails:
              $ref: "#/components/schemas/SupplierDetails"
      discriminator:
        propertyName: "type"

    OnRequestStatusAllow:
      description: If not set DBH will using internal false (KGA113)
      type: boolean
      default: false

    VirtualCreditCards:
      type: object
      required:
        - isVirtualCreditCard
      properties:
        isVirtualCreditCard:
          type: boolean
          default: false
        virtualCreditCards:
          type: array
          items:
            $ref: "#/components/schemas/VirtualCreditCard"

    VirtualCreditCard:
      type: object
      required:
        - internalCardKey
        - cardType
        - type
        - virtualCreditCardSupplierDetails
        - creationTimestamp
        - supplierPriceElements
      properties:
        internalCardKey:
          type: string
        supplierCode:
          type: string
        subSupplierCode:
          type: string
        cardType:
          $ref: "#/components/schemas/VirtualCreditCardType"
        type:
          $ref: "#/components/schemas/VirtualCreditCardVendor"
        virtualCreditCardSupplierDetails:
          $ref: "#/components/schemas/VirtualCreditCardSupplierDetails"
        creationTimestamp:
          description: KGA364
          type: string
          format: date-time
          example: "2018-06-13T14:13:00Z"
        modificationTimestamp:
          description: KGA364
          type: string
          format: date-time
          example: "2018-06-13T14:13:00Z"
        cancellationTimestamp:
          description: KGA364
          type: string
          format: date-time
          example: "2018-06-13T14:13:00Z"
        percentageTolerancePerCard:
          type: number
          format: float
          example: 4.0
        supplierPriceElements:
          $ref: "#/components/schemas/VirtualCreditCardSupplierPriceElement"

    VirtualCreditCardType:
      description: Possible values CREDIT, DEBIT, PREPAID
      enum:
        - CREDIT
        - DEBIT
        - PREPAID
      example: CREDIT

    VirtualCreditCardVendor:
      description: Possible values VISA, AMEX, MASTERCARD, DINERS (KGA14)
      enum:
        - VISA
        - AMEX
        - MASTERCARD
        - DINERS
      example: VISA

    VirtualCreditCardSupplierDetails:
      type: object
      required:
        - paymentSupplierId
        - paymentSubSupplierId
      properties:
        paymentSupplierId:
          type: string
        paymentSubSupplierId:
          type: string
        cardSupplierId:
          type: string

    VirtualCreditCardSupplierPriceElement:
      allOf:
        - $ref: "#/components/schemas/AbstractSupplierPriceElement"
        - type: object
          required:
            - purchaseRate
          properties:
            purchaseRate:
              type: array
              items:
                $ref: "#/components/schemas/VirtualCreditCardInDetailParent"

    VirtualCreditCardInDetailParent:
      allOf:
        - $ref: "#/components/schemas/MonetaryAmount"
        - type: object
          required:
            - properties
          properties:
            inDetail:
              $ref: "#/components/schemas/VirtualCreditCardInDetailChild"

    VirtualCreditCardInDetailChild:
      type: object
      required:
        - perCard
      properties:
        perCard:
          type: array
          items:
            $ref: "#/components/schemas/VirtualCreditCardInDetailPerCard"

    VirtualCreditCardInDetailPerCard:
      allOf:
        - $ref: "#/components/schemas/MonetaryAmount"
        - type: object
          required:
            - status
            - effectiveMinDate
            - effectiveMaxDate
          properties:
            status:
              $ref: "#/components/schemas/VirtualCreditCardInDetailStatus"
            effectiveMinDate:
              type: string
              format: date
              example: "2022-05-01"
            effectiveMaxDate:
              type: string
              format: date
              example: "2022-05-01"

    VirtualCreditCardInDetailStatus:
      description: Possible values INACTIVE, PENDING, DISPUTE, ACTIVE, CANCELLED, EXPIRED, SUBMITTED, SETTLED, ERROR, REFUNDED
      enum:
        - INACTIVE
        - PENDING
        - DISPUTE
        - ACTIVE
        - CANCELLED
        - EXPIRED
        - SUBMITTED
        - SETTLED
        - ERROR
        - REFUNDED
      example: INACTIVE