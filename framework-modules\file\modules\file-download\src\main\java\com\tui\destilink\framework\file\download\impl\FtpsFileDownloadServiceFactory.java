package com.tui.destilink.framework.file.download.impl;

import com.tui.destilink.framework.file.download.AbstractFileDownloadServiceFactory;
import com.tui.destilink.framework.file.download.DownloadConfiguration;
import com.tui.destilink.framework.file.download.DownloadType;
import com.tui.destilink.framework.file.storage.StorageService;
import org.springframework.core.io.support.ResourcePatternResolver;

public class FtpsFileDownloadServiceFactory implements AbstractFileDownloadServiceFactory {

    @Override
    public boolean canHandle(DownloadType type) {
        return DownloadType.FTPS == type;
    }

    @Override
    public AbstractFileDownloadService create(DownloadConfiguration configuration,
                                              StorageService storageService,
                                              ResourcePatternResolver resourcePatternResolver) {
        return new FtpsFileDownloadService(configuration.getHost(),
                configuration.getPort(),
                configuration.getUsername(),
                configuration.getPassword(),
                storageService);
    }

}
