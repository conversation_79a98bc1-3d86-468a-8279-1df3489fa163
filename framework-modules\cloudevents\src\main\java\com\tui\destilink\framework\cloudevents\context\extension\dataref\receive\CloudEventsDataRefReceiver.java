package com.tui.destilink.framework.cloudevents.context.extension.dataref.receive;

import com.tui.destilink.framework.cloudevents.exception.CloudEventsDataRefException;
import com.tui.destilink.framework.cloudevents.exception.CloudEventsException;
import io.cloudevents.CloudEventData;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.lang.NonNull;

import java.net.URI;
import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

public class CloudEventsDataRefReceiver {

    private final Collection<CloudEventsDataRefReceiverAdapter> adapters;

    public CloudEventsDataRefReceiver(ObjectProvider<CloudEventsDataRefReceiverAdapter> adapters) {
        this.adapters = adapters.orderedStream().toList();
    }

    public ImmutablePair<CloudEventData, String> resolve(@NonNull URI dataRef) throws CloudEventsDataRefException {
        try {
            final CloudEventsDataRefReceiverAdapter adapter = resolverAdapter(dataRef);
            if (adapter.isAsync()) {
                return adapter.receiveAsync(dataRef).join();
            } else {
                return adapter.receive(dataRef);
            }
        } catch (CancellationException ex) {
            throw new CloudEventsDataRefException("Failed to resolve CloudEvent dataRef with timeout or cancellation", ex);
        } catch (Exception ex) {
            Throwable result = ex;
            if (ex instanceof CompletionException ce) {
                result = ce.getCause();
            }
            if (result instanceof CloudEventsException cee) {
                throw cee;
            }
            throw new CloudEventsDataRefException("Failed to resolve CloudEvent dataRef", result);
        }
    }

    public CompletableFuture<ImmutablePair<CloudEventData, String>> resolveAsync(@NonNull URI dataRef) throws CloudEventsDataRefException {
        try {
            final CloudEventsDataRefReceiverAdapter adapter = resolverAdapter(dataRef);
            if (adapter.isAsync()) {
                return adapter.receiveAsync(dataRef);
            } else {
                return CompletableFuture.completedFuture(adapter.receive(dataRef));
            }
        } catch (Exception ex) {
            return CompletableFuture.failedFuture(new CloudEventsDataRefException("Failed to resolve CloudEvent dataRef", ex));
        }
    }

    private CloudEventsDataRefReceiverAdapter resolverAdapter(@NotNull URI dataRef) {
        Optional<CloudEventsDataRefReceiverAdapter> adapter = adapters.stream().filter(a -> a.supports(dataRef)).findFirst();
        if (adapter.isPresent()) {
            return adapter.get();
        }
        throw new IllegalArgumentException("Did not find resolver adapter for dataRef " + dataRef);
    }

}
