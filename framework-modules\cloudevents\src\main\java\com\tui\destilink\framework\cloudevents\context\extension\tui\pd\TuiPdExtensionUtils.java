package com.tui.destilink.framework.cloudevents.context.extension.tui.pd;

import io.cloudevents.CloudEvent;
import io.cloudevents.core.v1.CloudEventBuilder;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.tui.destilink.framework.cloudevents.context.extension.tui.pd.TuiPdExtension.PD_PERSON_REFS_KEY;

@Slf4j
@UtilityClass
public class TuiPdExtensionUtils {

    private static final Pattern ID_REPLACE_PATTERN = Pattern.compile("(id\"\\s*:\\s*\")((?:\\\\\"|[^\"])*)");

    public static CloudEvent tryAnonymize(CloudEvent cloudEvent) {
        try {
            Object pdPersonRefs = cloudEvent.getExtension(PD_PERSON_REFS_KEY);
            if (pdPersonRefs == null) {
                return cloudEvent;
            }
            if (pdPersonRefs instanceof String s) {
                return new CloudEventBuilder(cloudEvent)
                        .withExtension(PD_PERSON_REFS_KEY, anonymize(s))
                        .build();
            }
            log.error("Failed to anonymize CloudEvent {} because is not type String but {}", PD_PERSON_REFS_KEY, pdPersonRefs.getClass());
            return cloudEvent;
        } catch (Exception ex) {
            cloudEvent = new CloudEventBuilder(cloudEvent)
                    .withoutExtension(PD_PERSON_REFS_KEY)
                    .build();
            log.error("Failed to anonymize CloudEvent {}, removing value instead", PD_PERSON_REFS_KEY, ex);
            return cloudEvent;
        }
    }

    public static String anonymize(String pdPersonRefs) {
        Matcher matcher = ID_REPLACE_PATTERN.matcher(pdPersonRefs);
        if (matcher.find()) {
            return matcher.replaceAll("$1REDACTED");
        }
        return pdPersonRefs;
    }

}
