package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.download.impl.AbstractFileDownloadService;
import com.tui.destilink.framework.file.storage.StorageService;
import org.springframework.core.io.support.ResourcePatternResolver;

public interface AbstractFileDownloadServiceFactory {

    boolean canHandle(DownloadType type);

    AbstractFileDownloadService create(DownloadConfiguration configuration,
                                       StorageService storageService,
                                       ResourcePatternResolver resourcePatternResolver);
}
