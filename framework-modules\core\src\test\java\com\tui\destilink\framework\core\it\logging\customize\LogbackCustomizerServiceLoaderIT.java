package com.tui.destilink.framework.core.it.logging.customize;

import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.tui.destilink.framework.core.it.TestApplication;
import com.tui.destilink.framework.core.logging.marker.ConstraintViolationMarker;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@Slf4j
// JSON logging must be enabled for this test
@ActiveProfiles("logback-json-logging")
@SpringBootTest(classes = {TestApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ExtendWith(OutputCaptureExtension.class)
class LogbackCustomizerServiceLoaderIT {

    static class Broken {
        @NotNull
        private String name;
        @Valid
        @NotNull
        private final NestedBroken nestedBroken = new NestedBroken();

        static class NestedBroken {
            @NotBlank
            private final String nestedName = "";
        }
    }

    private static final String LOG_MSG = "Constraint violation logged";

    @Autowired
    private Validator validator;

    @Test
    void testConstraintViolationMarker(CapturedOutput capturedOutput) {
        Set<ConstraintViolation<Broken>> constraintViolations = validator.validate(new Broken());
        ConstraintViolationMarker marker = ConstraintViolationMarker.of(constraintViolations);
        log.error(marker, LOG_MSG);

        await().until(() -> capturedOutput.getOut().contains(LOG_MSG));

        validateOutput(capturedOutput);
    }

    @Test
    void testConstraintViolationException(CapturedOutput capturedOutput) {
        Set<ConstraintViolation<Broken>> constraintViolations = validator.validate(new Broken());
        ConstraintViolationException ex = new ConstraintViolationException("Some violations", constraintViolations);
        log.error(LOG_MSG, ex);

        await().until(() -> capturedOutput.getOut().contains(LOG_MSG));

        validateOutput(capturedOutput);
    }

    private void validateOutput(CapturedOutput capturedOutput) {
        String[] linesArr = capturedOutput.getOut().split(System.lineSeparator());

        List<String> lines = Arrays.stream(linesArr).filter(l -> l.contains(LOG_MSG)).toList();
        assertThat(lines).hasSize(1);

        DocumentContext docCtx = JsonPath.parse(lines.getFirst());
        assertThat((JSONArray) docCtx.read("$." + ConstraintViolationMarker.FIELD_NAME + ".*.propertyPath"))
                .hasSize(2)
                .contains("nestedBroken.nestedName", "name");

        assertThat((JSONArray) docCtx.read("$." + ConstraintViolationMarker.FIELD_NAME + ".*.message"))
                .hasSize(2); // Message language depends on system language
    }
}
