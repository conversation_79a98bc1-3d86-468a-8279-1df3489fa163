package com.tui.destilink.framework.cloudevents.exception;

import com.tui.destilink.framework.cloudevents.logging.marker.CloudEventsMarker;
import com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException;
import io.cloudevents.CloudEvent;
import net.logstash.logback.marker.Markers;

public class CloudEventsException extends MarkerNestedRuntimeException {
    public CloudEventsException(String msg) {
        super(Markers.empty(), msg);
    }

    public CloudEventsException(String msg, Throwable cause) {
        super(Markers.empty(), msg, cause);
    }

    public CloudEventsException(CloudEvent cloudevent, String msg) {
        super(CloudEventsMarker.of(cloudevent), msg);
    }

    public CloudEventsException(CloudEvent cloudevent, String msg, Throwable cause) {
        super(CloudEventsMarker.of(cloudevent), msg, cause);
    }
}
