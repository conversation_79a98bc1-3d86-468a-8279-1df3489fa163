package com.tui.destilink.framework.redis.core;

import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.redis.core.customize.LettuceConnectionFactoryBeanPostProcessor;
import com.tui.destilink.framework.redis.core.customize.LettuceConnectionFactoryCustomizer;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import io.lettuce.core.metrics.MicrometerCommandLatencyRecorder;
import io.lettuce.core.metrics.MicrometerOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.ClientResourcesBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

@AutoConfiguration
@AutoConfigureBefore(RedisAutoConfiguration.class)
@EnableConfigurationProperties(RedisCoreProperties.class)
public class RedisCoreAutoConfiguration {

    private final RedisCoreProperties redisCoreProperties;

    @Autowired
    RedisCoreAutoConfiguration(RedisCoreProperties redisCoreProperties) {
        this.redisCoreProperties = redisCoreProperties;
    }

    @Bean
    static LettuceConnectionFactoryBeanPostProcessor lettuceConnectionFactoryBeanPostProcessor(ObjectProvider<LettuceConnectionFactoryCustomizer> customizers) {
        return new LettuceConnectionFactoryBeanPostProcessor(customizers);
    }

    @Bean
    @ConditionalOnBean(RedisConnectionFactory.class)
    RedisClusterClient redisClusterClient(RedisConnectionFactory redisConnectionFactory) {
        return (RedisClusterClient) ((LettuceConnectionFactory) redisConnectionFactory).getNativeClient();
    }

    @Bean
    LettuceConnectionFactoryCustomizer shareNativeConnectionLettuceFactoryCustomizer() {
        return factory -> factory.setShareNativeConnection(redisCoreProperties.getLettuce().getShareNativeConnection());
    }

    @Bean
    @ConditionalOnBean(MeterRegistry.class)
    ClientResourcesBuilderCustomizer micrometerClientResourcesBuilderCustomizer(MeterRegistry meterRegistry) {
        return builder -> builder
                .commandLatencyRecorder(new MicrometerCommandLatencyRecorder(meterRegistry, MicrometerOptions.create()));
    }

    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.client-name")
    LettuceClientConfigurationBuilderCustomizer lettuceClientConfigurationClientNameCustomizer(@Value("${spring.data.redis.client-name}") String clientName) {
        return builder -> builder.clientName(clientName);
    }

    @Bean
    LettuceClientConfigurationBuilderCustomizer lettuceClientConfigurationClusterCustomizer() {
        return clientConfigurationBuilder -> {
            ClientOptions source = clientConfigurationBuilder.build().getClientOptions().orElseThrow();
            ClientOptions.Builder builder = ClusterClientOptions.builder(source)
                    .protocolVersion(ProtocolVersion.RESP3)
                    .timeoutOptions(buildTimeoutOptions())
                    .socketOptions(modifySocketOptions(source.getSocketOptions()))
                    .topologyRefreshOptions(buildClusterTopologyRefreshOptions())
                    .autoReconnect(true)
                    .nodeFilter(it ->
                            !(it.is(RedisClusterNode.NodeFlag.FAIL)
                                    || it.is(RedisClusterNode.NodeFlag.HANDSHAKE)
                                    || it.is(RedisClusterNode.NodeFlag.EVENTUAL_FAIL)
                                    || it.is(RedisClusterNode.NodeFlag.NOADDR)))
                    .validateClusterNodeMembership(false);
            clientConfigurationBuilder
                    .clientOptions(builder.build())
                    .commandTimeout(redisCoreProperties.buildCommandsTimeoutSource().getHighestTimeout());
        };
    }

    private ClusterTopologyRefreshOptions buildClusterTopologyRefreshOptions() {
        return ClusterTopologyRefreshOptions.builder()
                .enableAllAdaptiveRefreshTriggers()
                .enablePeriodicRefresh()
                .dynamicRefreshSources(true)
                .build();
    }

    private TimeoutOptions buildTimeoutOptions() {
        return TimeoutOptions.builder()
                .timeoutSource(redisCoreProperties.buildCommandsTimeoutSource())
                .build();
    }

    private SocketOptions modifySocketOptions(SocketOptions socketOptions) {
        return socketOptions.mutate()
                .connectTimeout(redisCoreProperties.getSocketOptions().getConnectTimeout())
                .keepAlive(redisCoreProperties.getSocketOptions().getKeepAlive())
                .build();
    }
}
