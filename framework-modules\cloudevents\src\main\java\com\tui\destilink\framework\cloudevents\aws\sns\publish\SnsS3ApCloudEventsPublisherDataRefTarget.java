package com.tui.destilink.framework.cloudevents.aws.sns.publish;

import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherDataRefTarget;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.arns.Arn;

import java.net.URI;

@Getter
@SuperBuilder(toBuilder = true)
public class SnsS3ApCloudEventsPublisherDataRefTarget extends CloudEventsPublisherDataRefTarget {

    public enum S3ApDataRefScope {INTERNAL, EXTERNAL}

    public static final String S3_PROTOCOL = "s3://";

    private final S3ApDataRefScope scope;
    private final Arn apArn;
    private final String apId;
    private final String apName;
    private final String apAlias;
    private final String apDomainName;
    private final String apPrefix;

    public static URI buildBaseUri(String apAlias, String apPrefix) {
        StringBuilder builder = new StringBuilder(S3_PROTOCOL);
        builder.append(apAlias);
        if (!apPrefix.startsWith("/")) {
            builder.append("/");
        }
        builder.append(apPrefix);
        if (!apPrefix.endsWith("/")) {
            builder.append("/");
        }
        return URI.create(builder.toString());
    }
}
