package com.tui.destilink.framework.core.logging;

import lombok.experimental.UtilityClass;

@UtilityClass
public class UnicodeUtils {

    public static final int END_CODEPOINT_1_BYTE = 0x007F;
    public static final int END_CODEPOINT_2_BYTE = 0x07FF;
    public static final int END_CODEPOINT_3_BYTE_NO_SURROGATE = Character.MIN_SURROGATE - 1;
    public static final int END_CODEPOINT_3_BYTE_END_LOW_SURROGATE = Character.MAX_SURROGATE;

    public static String truncate(String s, int byteLength) {
        int b = 0;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            // ranges from http://en.wikipedia.org/wiki/UTF-8
            int skip = 0;
            int more;

            if (c <= END_CODEPOINT_1_BYTE) {
                more = 1;
            } else if (c <= END_CODEPOINT_2_BYTE) {
                more = 2;
            } else if (c <= END_CODEPOINT_3_BYTE_NO_SURROGATE) {
                more = 3;
            } else if (c <= END_CODEPOINT_3_BYTE_END_LOW_SURROGATE) { // WTF-8
                // surrogate area, consume next char as well
                more = 4;
                skip = 1;
            } else {
                more = 3;
            }

            if (b + more > byteLength) {
                return s.substring(0, i);
            }
            b += more;
            i += skip; // NOSONAR
        }
        return s;
    }

    public static int encodedUTF8Length(String s) {
        int b = 0;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            // ranges from http://en.wikipedia.org/wiki/UTF-8
            int skip = 0;
            int more;
            if (c <= END_CODEPOINT_1_BYTE) {
                more = 1;
            } else if (c <= END_CODEPOINT_2_BYTE) {
                more = 2;
            } else if (c <= END_CODEPOINT_3_BYTE_NO_SURROGATE) {
                more = 3;
            } else if (c <= END_CODEPOINT_3_BYTE_END_LOW_SURROGATE) { // WTF-8
                // surrogate area, consume next char as well
                more = 4;
                skip = 1;
            } else {
                more = 3;
            }
            b += more;
            i += skip; // NOSONAR
        }
        return b;
    }
}
