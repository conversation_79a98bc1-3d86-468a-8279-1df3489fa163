package com.tui.destilink.framework.caching.condition;

import com.tui.destilink.framework.caching.config.CachingProperties;
import com.tui.destilink.framework.caching.config.CachingProperties.BackendBindingType;
import com.tui.destilink.framework.core.util.ConfigurationPropertyUtils;
import org.springframework.boot.autoconfigure.condition.ConditionMessage;
import org.springframework.boot.autoconfigure.condition.ConditionOutcome;
import org.springframework.boot.autoconfigure.condition.SpringBootCondition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Optional;

public class OnCacheBackendCondition extends SpringBootCondition {

    @Override
    public ConditionOutcome getMatchOutcome(ConditionContext context, AnnotatedTypeMetadata metadata) {
        Optional<BackendBindingType> optionalType = metadata.getAnnotations()
                .get(ConditionalOnCacheBackend.class)
                .getValue("value", CachingProperties.BackendBindingType.class);
        if (optionalType.isEmpty()) {
            throw new IllegalStateException("Did not find value with BackendBindingType on annotation " + metadata);
        }

        BackendBindingType type = optionalType.get();
        CachingProperties cachingProps = ConfigurationPropertyUtils
                .loadAndValidate(context.getEnvironment(), CachingProperties.PROPERTIES_PREFIX, CachingProperties.class);

        boolean hasBackendForType = cachingProps.hasBackendForType(type);
        if (hasBackendForType) {
            return ConditionOutcome.match(ConditionMessage
                    .forCondition(ConditionalOnCacheBackend.class)
                    .found("type", "types").items(type));
        }

        return ConditionOutcome.noMatch(ConditionMessage
                .forCondition(ConditionalOnCacheBackend.class)
                .didNotFind("type", "types").items(type));
    }

}
