package com.tui.destilink.framework.trips.core.logging;

import lombok.experimental.UtilityClass;

// Datadog logging attributes for events
// For simplicity these constants should not be used as of now.
// Decision might change to set individually values if needed.

// Workaround is a datadog parser that copies them:
// if level=ERROR then
// stack_trace -> error.stack
// logger_name or throwing class if stack_trace -> error.kind
// message -> error.message
@UtilityClass
public class ErrorContextConstants {

    public static final String PREFIX = "error";
    private static final String KIND = "kind";
    private static final String STACK = "stack";
    private static final String MESSAGE = "message";
    private static final String LOCATION = "location";
    private static final String SEPARATOR = ".";
    // mandatory
    public static final String ERROR_KIND = PREFIX + SEPARATOR + KIND; // business-logic specific error code or an exception class
    public static final String ERROR_STACK = PREFIX + SEPARATOR + STACK; // stacktrace (if present)
    // recommended
    public static final String ERROR_MESSAGE = PREFIX + SEPARATOR + MESSAGE;
    public static final String ERROR_LOCATION = PREFIX + SEPARATOR + LOCATION;
    private static final String ERROR_CAUSE_PREFIX = PREFIX + SEPARATOR + "cause";
    // optional
    public static final String ERROR_CAUSE_KIND = ERROR_CAUSE_PREFIX + SEPARATOR + KIND;
    public static final String ERROR_CAUSE_MESSAGE = ERROR_CAUSE_PREFIX + SEPARATOR + MESSAGE;
}
