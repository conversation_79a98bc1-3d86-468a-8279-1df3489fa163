package com.tui.destilink.framework.cloudevents.aws.sns.publish;

import com.tui.destilink.framework.aws.sns.resolver.TopicTagsResolver;
import io.awspring.cloud.sns.core.TopicArnResolver;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.lang.Nullable;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sns.model.Tag;

import java.util.*;

public class SnsCloudEventsTargetResolver {

    @Data
    @Builder
    @AllArgsConstructor(access = AccessLevel.PROTECTED)
    public static class SnsTarget {
        public enum Type {INTERNAL, EXTERNAL}

        @NotBlank
        private final String name;
        private final Arn arn;
        @NotBlank
        private final String s3AccessPointAlias;
        @Nullable
        private final String s3AccessPointArn;
        @Nullable
        private final String s3AccessPointDomainName;
        @Nullable
        private final String s3AccessPointId;
        @Nullable
        private final String s3AccessPointName;
        @NotBlank
        private final String s3Prefix;
        @NotNull
        private final Type type;
    }

    private final Map<Arn, SnsTarget> arnSnsTargetMap = new HashMap<>();

    private final TopicArnResolver topicArnResolver;
    private final TopicTagsResolver topicTagsResolver;
    private final Validator validator;

    public SnsCloudEventsTargetResolver(TopicArnResolver topicArnResolver, TopicTagsResolver topicTagsResolver, Validator validator) {
        this.topicArnResolver = topicArnResolver;
        this.topicTagsResolver = topicTagsResolver;
        this.validator = validator;
    }

    public SnsTarget resolve(String name) {
        return resolve(topicArnResolver.resolveTopicArn(name));
    }

    public SnsTarget resolve(Arn arn) {
        return arnSnsTargetMap.computeIfAbsent(arn, this::createSnsTarget);
    }

    private SnsTarget createSnsTarget(Arn arn) {
        List<Tag> tags = topicTagsResolver.resolveTopicTags(arn);
        SnsTarget.SnsTargetBuilder builder = SnsTarget.builder()
                .name(arn.resource().resource()).arn(arn);
        tags.forEach(tag -> build(tag.key(), tag.value(), builder));
        SnsTarget snsTarget = builder.build();
        Set<ConstraintViolation<SnsTarget>> violations = validator.validate(snsTarget);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException("Failed to load CloudEvent tags from SNS topic " + arn, violations);
        }
        return snsTarget;
    }

    private void build(String key, String value, SnsTarget.SnsTargetBuilder builder) {
        switch (key) {
            case "cloudevents-s3-accesspoint-alias" -> builder.s3AccessPointAlias(value);
            case "cloudevents-s3-accesspoint-arn" -> builder.s3AccessPointArn(value);
            case "cloudevents-s3-accesspoint-domain-name" -> builder.s3AccessPointDomainName(value);
            case "cloudevents-s3-accesspoint-id" -> builder.s3AccessPointId(value);
            case "cloudevents-s3-accesspoint-name" -> builder.s3AccessPointName(value);
            case "cloudevents-s3-prefix" -> builder.s3Prefix(value);
            case "cloudevents-type" -> builder.type(SnsTarget.Type.valueOf(value.toUpperCase(Locale.ROOT)));
            default -> {
                // NOSONAR - Sonar wants a default but does not like an empty one - FU Sonar
            } // Empty default to make sonar happy
        }
    }
}
