package com.tui.destilink.framework.redis.core.config;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.protocol.CommandType;
import io.lettuce.core.protocol.RedisCommand;
import lombok.Getter;

import java.time.Duration;
import java.util.EnumMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

public class CommandsTimeoutSource extends TimeoutOptions.TimeoutSource {
    public static final Set<CommandType> META_COMMAND_TYPES = ImmutableSet.<CommandType>builder()
            .add(CommandType.FLUSHDB)
            .add(CommandType.FLUSHALL)
            .add(CommandType.CLUSTER)
            .add(CommandType.INFO)
            .add(CommandType.KEYS)
            .build();

    private final Duration defaultTimeout;
    private final Map<CommandType, Duration> timeouts;

    @Getter
    private final Duration highestTimeout;

    protected CommandsTimeoutSource(RedisCoreProperties properties) {
        this(properties.getCommandTimeouts().getDefaultCommandTimeout(), buildMetaTimeouts(properties));
    }

    public CommandsTimeoutSource(Duration defaultTimeout, Map<CommandType, Duration> timeouts) {
        this.defaultTimeout = defaultTimeout;
        this.timeouts = ImmutableMap.copyOf(timeouts);
        this.highestTimeout = findHighestTimeout(defaultTimeout, timeouts);
    }

    @Override
    public long getTimeout(RedisCommand<?, ?, ?> command) {
        if (command.getType() instanceof CommandType ct) {
            return timeouts.getOrDefault(ct, defaultTimeout).toMillis();
        }
        return defaultTimeout.toMillis();
    }

    private static Map<CommandType, Duration> buildMetaTimeouts(RedisCoreProperties properties) {
        Map<CommandType, Duration> timeouts = new EnumMap<>(CommandType.class);
        if (properties.getCommandTimeouts().getMetaCommandTimeout() != null) {
            META_COMMAND_TYPES.forEach(
                    commandType -> timeouts.put(commandType, properties.getCommandTimeouts().getMetaCommandTimeout()));
        }
        timeouts.putAll(properties.getCommandTimeouts().getOverrideTimeouts());
        return timeouts;
    }

    private static Duration findHighestTimeout(Duration defaultTimeout, Map<CommandType, Duration> timeouts) {
        return Stream.concat(Stream.of(defaultTimeout), timeouts.values().stream())
                .max(Duration::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("Did not find suitable highest redis command timeout value"));
    }
}
