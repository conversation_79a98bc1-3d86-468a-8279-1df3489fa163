package com.tui.destilink.framework.file.storage;

import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface StorageService {

    /**
     * Stores the entry with the provided filename.
     *
     * @return One or more {@link Resource} that represent the stored entry.
     */
    List<Resource> store(String filename, InputStream inputStream) throws IOException;

    /**
     * Loads the entry with the provided filename and returns the {@link InputStream} for reading.
     * The {@link InputStream} must be closed after reading!
     */
    default InputStream load(String filename) throws IOException {
        throw new UnsupportedOperationException();
    }

    /**
     * Deletes the entry with the provided filename.
     */
    default void delete(String filename) throws IOException {
        throw new UnsupportedOperationException();
    }

    /**
     * Lists all entries that match the provided ant-style pattern.
     */
    default List<String> list(String resourcePattern) throws IOException {
        throw new UnsupportedOperationException();
    }

    /**
     * Renames the name from a tempname into the final one. See DLI-3784 for more information.
     */
    default List<Resource> rename(String oldName, String newName) throws IOException {
        throw new UnsupportedOperationException();
    }

    /*
     * Lists all resources that match the provided ant-style pattern.
     * E.g. useful to directly query lastModifiedDate from Resources
     */
    default List<Resource> listResources(String resourcePattern) throws IOException {
        throw new UnsupportedOperationException();
    }

}
