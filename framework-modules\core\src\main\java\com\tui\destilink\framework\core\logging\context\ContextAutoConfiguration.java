package com.tui.destilink.framework.core.logging.context;

import com.tui.destilink.framework.core.logging.LoggingProperties;
import com.tui.destilink.framework.core.logging.context.global.GlobalContextProvider;
import com.tui.destilink.framework.core.logging.context.global.GlobalLoggingContextSetupListener;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;

import java.util.Map;

@AutoConfiguration
public class ContextAutoConfiguration {

    @Bean
    ApplicationListener<ApplicationStartedEvent> globalLoggingContextSetupListener(ObjectProvider<GlobalContextProvider> globalContextProviders) {
        return new GlobalLoggingContextSetupListener(globalContextProviders);
    }

    @Bean
    @ConditionalOnProperty(prefix = LoggingProperties.LoggingContextProperties.PREFIX, name = "layer")
    GlobalContextProvider layerGlobalContextProvider(LoggingProperties props) {
        return () -> Map.of("contextMap.marker.layer", props.getContext().getLayer());
    }

    @Bean
    @ConditionalOnProperty(prefix = LoggingProperties.DsgvoContextProperties.PREFIX, name = "enabled")
    GlobalContextProvider dsgvoGlobalContextProvider(LoggingProperties props) {
        return () -> Map.of(props.getContext().getDsgvo().getTag(), "true");
    }
}
