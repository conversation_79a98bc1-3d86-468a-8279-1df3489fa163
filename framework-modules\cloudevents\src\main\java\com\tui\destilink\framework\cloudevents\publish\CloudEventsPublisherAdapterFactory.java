package com.tui.destilink.framework.cloudevents.publish;

import com.tui.destilink.framework.cloudevents.config.CloudEventsConfigProperties.PublisherProperties;
import com.tui.destilink.framework.cloudevents.context.extension.dataref.publish.CloudEventsDataRefPublisher;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.Map;

public abstract class CloudEventsPublisherAdapterFactory {

    private final Map<CloudEventsPublisherContext<?, ?>, CloudEventsPublisherAdapter<?, ?, ?>> adapterCache = new ConcurrentReferenceHashMap<>(2, ConcurrentReferenceHashMap.ReferenceType.WEAK);

    public abstract <T, C extends CloudEventsPublisherContext<T, R>, R extends CloudEventsPublishResult> boolean supports(C context);

    @SuppressWarnings("unchecked")
    public <T, C extends CloudEventsPublisherContext<T, R>, R extends CloudEventsPublishResult> CloudEventsPublisherAdapter<T, C, R> instance(C context, CloudEventsDataRefPublisher dataRefPublisher, PublisherProperties publisherProps) {
        return (CloudEventsPublisherAdapter<T, C, R>) adapterCache.computeIfAbsent(context, k -> createInstance(context, dataRefPublisher, publisherProps));
    }

    protected abstract <T, C extends CloudEventsPublisherContext<T, R>, R extends CloudEventsPublishResult> CloudEventsPublisherAdapter<?, ?, ?> /*NOSONAR*/ createInstance(C context, CloudEventsDataRefPublisher dataRefPublisher, PublisherProperties publisherProps);
}
