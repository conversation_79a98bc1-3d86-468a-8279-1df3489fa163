package com.tui.destilink.framework.cloudevents.aws.s3.publish.dataref;

import com.tui.destilink.framework.cloudevents.context.extension.dataref.publish.CloudEventsDataRefPublisherAdapter;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import org.springframework.lang.NonNull;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Uri;
import software.amazon.awssdk.services.s3.S3Utilities;

import java.net.URI;
import java.util.concurrent.CompletableFuture;

public class S3CloudEventsDataRefPublisherAdapter extends CloudEventsDataRefPublisherAdapter {

    private final S3AsyncClient s3Client;
    private final S3Utilities s3Utilities;

    public S3CloudEventsDataRefPublisherAdapter(S3AsyncClient s3Client) {
        super(true);
        this.s3Client = s3Client;
        this.s3Utilities = s3Client.utilities();
    }

    @Override
    public boolean supports(@NonNull CloudEvent cloudEvent, @NonNull URI dataRef) {
        return "s3".equalsIgnoreCase(dataRef.getScheme());
    }

    @Override
    protected CompletableFuture<URI> doPublishAsync(@NonNull CloudEventData data, @NonNull URI dataRef, @NonNull String dataContentType) {
        S3Uri s3Uri = s3Utilities.parseUri(dataRef);
        return s3Client.putObject(b -> b
                                .bucket(s3Uri.bucket().orElseThrow())
                                .key(s3Uri.key().orElseThrow())
                                .contentType(dataContentType)
                                // Do not allow to override files
                                .overrideConfiguration(o -> o.putHeader("If-None-Match", "*")),
                        AsyncRequestBody.fromBytes(data.toBytes()))
                .thenApply(r -> dataRef);
    }
}
