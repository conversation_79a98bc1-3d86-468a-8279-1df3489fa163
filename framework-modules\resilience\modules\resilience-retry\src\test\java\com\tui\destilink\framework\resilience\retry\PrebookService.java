package com.tui.destilink.framework.resilience.retry;

import io.github.resilience4j.retry.annotation.Retry;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class PrebookService {

    @Retry(name = "adapterRetryApi")
    public String process(String data) {
        return data;
    }

    @Retry(name = "testRetryNoop")
    public String processNoRetry(String data) {
        return data;
    }

    @Retry(name = "testRetry")
    public String processSpecificRetry(String data) {
        return data;
    }
}
