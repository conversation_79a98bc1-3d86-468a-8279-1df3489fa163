package com.tui.destilink.framework.redis.core.cluster.pool;

import com.google.common.collect.Iterables;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class CycleClusterConnectionPool implements InitializingBean, DisposableBean {

    public static final int CONNECTION_COUNT_DEFAULT = 5;

    private final RedisClusterClient redisClusterClient;
    private final int connectionCount;

    private final Lock nonBlockingConnectionsLock = new ReentrantLock();
    private final List<StatefulRedisClusterConnection<String, String>> nonBlockingConnectionsList = new CopyOnWriteArrayList<>();
    private final Iterator<StatefulRedisClusterConnection<String, String>> nonBlockingConnections = Iterables.cycle(nonBlockingConnectionsList).iterator();

    private boolean initialized = false;

    public CycleClusterConnectionPool(RedisClusterClient redisClusterClient) {
        this(redisClusterClient, CONNECTION_COUNT_DEFAULT);
    }

    public CycleClusterConnectionPool(RedisClusterClient redisClusterClient, int connectionCount) {
        this.redisClusterClient = redisClusterClient;
        this.connectionCount = connectionCount;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        nonBlockingConnectionsLock.lock();
        try {
            if (!initialized) {
                for (int i = 0; i < connectionCount; i++) {
                    var con = redisClusterClient.connect();
                    nonBlockingConnectionsList.add(con);
                }
                initialized = true;
            }
        } finally {
            nonBlockingConnectionsLock.unlock();
        }
    }

    @Override
    public void destroy() {
        nonBlockingConnectionsLock.lock();
        try {
            initialized = false;
            nonBlockingConnectionsList.forEach(StatefulRedisClusterConnection::close);
        } finally {
            nonBlockingConnectionsLock.unlock();
        }
    }

    public CompletableFuture<StatefulRedisClusterConnection<String, String>> getClusterConnection() {
        nonBlockingConnectionsLock.lock();
        try {
            if (nonBlockingConnections.hasNext()) {
                return CompletableFuture.completedFuture(nonBlockingConnections.next());
            }
            return CompletableFuture.failedFuture(new NoSuchElementException("NonBlockingConnections is empty"));
        } finally {
            nonBlockingConnectionsLock.unlock();
        }
    }
}
