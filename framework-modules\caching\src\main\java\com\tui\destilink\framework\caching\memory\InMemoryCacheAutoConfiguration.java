package com.tui.destilink.framework.caching.memory;

import com.tui.destilink.framework.caching.CachingAutoConfiguration;
import com.tui.destilink.framework.caching.cachemanager.CacheManagersProvider;
import com.tui.destilink.framework.caching.condition.ConditionalOnCacheBackend;
import com.tui.destilink.framework.caching.config.CachingConfigProvider;
import com.tui.destilink.framework.caching.config.CachingProperties;
import com.tui.destilink.framework.caching.memory.cache.InMemoryCacheManager;
import com.tui.destilink.framework.caching.memory.config.InMemoryCacheProperties;
import com.tui.destilink.framework.core.condition.ConditionalOnPropertyPrefix;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;

import java.util.HashMap;
import java.util.Map;

@EnableScheduling
@AutoConfiguration
@AutoConfigureAfter(CachingAutoConfiguration.class)
@EnableConfigurationProperties(InMemoryCacheProperties.class)
@ConditionalOnPropertyPrefix(InMemoryCacheProperties.PROPERTIES_PREFIX)
@ConditionalOnCacheBackend(CachingProperties.BackendBindingType.IN_MEMORY)
@ConditionalOnBean(CachingConfigProvider.class)
public class InMemoryCacheAutoConfiguration {

    @Bean
    public CacheManagersProvider<InMemoryCacheManager> inMemoryCacheManagersProvider(CachingConfigProvider cachingConfig, InMemoryCacheProperties inMemoryCacheProps) {
        Map<String, InMemoryCacheManager> cacheManagers = HashMap.newHashMap(inMemoryCacheProps.getInstances().size());
        inMemoryCacheProps.getInstances().forEach(i -> {
            String keyspacePrefix = i.getKeyspacePrefixOrDefault(cachingConfig);
            cacheManagers.put(keyspacePrefix, InMemoryCacheManager.create(cachingConfig, i));
        });
        return new CacheManagersProvider<>(cacheManagers);
    }

    @Bean
    public SchedulingConfigurer inMemoryCacheSchedulingConfigurer(CachingConfigProvider cachingConfig, InMemoryCacheProperties inMemoryCacheProps, CacheManagersProvider<InMemoryCacheManager> cacheManagersProvider) {
        return taskRegistrar -> inMemoryCacheProps.getInstances().forEach(i -> {
            InMemoryCacheManager cm;
            if (i.getKeyspacePrefix() == null) {
                cm = cacheManagersProvider.getCacheManagers().get(cachingConfig.getCacheDefaults().getKeyspacePrefix());
            } else {
                cm = cacheManagersProvider.getCacheManagers().get(i.getKeyspacePrefix());
            }
            taskRegistrar.addCronTask(cm, i.getCleanupCron());
        });
    }

}
