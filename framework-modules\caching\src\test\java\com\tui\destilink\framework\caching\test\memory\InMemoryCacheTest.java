package com.tui.destilink.framework.caching.test.memory;

import com.tui.destilink.framework.caching.cachemanager.CompositeCacheManager;
import com.tui.destilink.framework.caching.memory.cache.ExpirableConcurrentMapCache;
import com.tui.destilink.framework.caching.test.TestApplication;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.ReflectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ActiveProfiles({"in-memory"})
@SpringBootTest(
        classes = {TestApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class InMemoryCacheTest {

    @Autowired
    private CompositeCacheManager cacheManager;

    @Test
    void testCacheManager() {
        assertThat(cacheManager.getDefaultCacheManager()).isNotNull();
        assertThat(cacheManager.getCacheManagers()).hasSize(2);
        assertThat(cacheManager.getCaches()).hasSize(5);
        assertThat(cacheManager.getCacheNames()).hasSize(5);
    }

    @Test
    @DirtiesContext
    void testRuntimeCacheCreation() {
        assertThat(cacheManager.getCaches()).hasSize(5);
        assertThat(cacheManager.getCacheNames()).hasSize(5);
        assertThat(cacheManager.getDefaultCacheManager().getCacheNames()).hasSize(4);
        assertThat(cacheManager.getCache("cache1")).isNotNull();
        assertThat(cacheManager.getCache("runtime-cache")).isNotNull();
        assertThat(cacheManager.getCaches()).hasSize(6);
        assertThat(cacheManager.getCacheNames()).hasSize(6);
        assertThat(cacheManager.getDefaultCacheManager().getCacheNames()).hasSize(5);
    }

    @Test
    @DirtiesContext
    void testScheduledDeleteExpired() {
        // cache1 has a ttl value of 24h and cache2 only 5s
        ExpirableConcurrentMapCache cache1 = (ExpirableConcurrentMapCache) cacheManager.getCache("cache1");
        ExpirableConcurrentMapCache cache3 = (ExpirableConcurrentMapCache) cacheManager.getCache("cache3");

        int cache1Size = cache1.getNativeCache().size();
        int cache3Size = cache3.getNativeCache().size();

        for (int i = 0; i < 100; i++) {
            cache1.put("key" + i, "Hello World " + i);
            cache3.put("key" + i, "Hello World " + i);
        }

        // Test if all objects where written
        for (int i = 0; i < 100; i++) {
            assertThat(cache1.get("key" + i, String.class)).isEqualTo("Hello World " + i);
            assertThat(cache3.get("key" + i, String.class)).isEqualTo("Hello World " + i);
        }

        assertThat(cache1.getNativeCache()).hasSize(100 + cache1Size);
        assertThat(cache3.getNativeCache()).hasSize(100 + cache3Size);

        // cache3 with a ttl of 5s should be empty after at most 10s
        await().atMost(Duration.ofSeconds(10)).pollInterval(Duration.ofSeconds(1))
                .until(() -> cache3.getNativeCache().isEmpty());
        assertThat(cache1.getNativeCache()).hasSize(100 + cache1Size);
    }

    @Test
    @DirtiesContext
    void testAllowNullValues() {
        ExpirableConcurrentMapCache enableTimeToIdleCache = (ExpirableConcurrentMapCache) cacheManager.getCache("enableTimeToIdleCache");
        ExpirableConcurrentMapCache allowNullCache = (ExpirableConcurrentMapCache) cacheManager.getCache("allowNullValuesCache");

        assertThrows(IllegalArgumentException.class, () -> enableTimeToIdleCache.put("key", null));
        allowNullCache.put("key", null);

        assertThat(enableTimeToIdleCache.get("key")).isNull();
        assertThat(allowNullCache.get("key").get()).isNull();
    }

    @Test
    @DirtiesContext
    void testEnableTimeToIdle() throws Exception {
        ExpirableConcurrentMapCache enableTimeToIdleCache = (ExpirableConcurrentMapCache) cacheManager.getCache("enableTimeToIdleCache");
        ExpirableConcurrentMapCache allowNullCache = (ExpirableConcurrentMapCache) cacheManager.getCache("allowNullValuesCache");
        enableTimeToIdleCache.put("key", "Hello World");
        allowNullCache.put("key", "Hello World");

        LocalDateTime enabledInitial = getExpirationTime(enableTimeToIdleCache.getNativeCache().get("key"));
        LocalDateTime disabledInitial = getExpirationTime(allowNullCache.getNativeCache().get("key"));

        // Just wait for 1s
        await().pollDelay(Duration.ofSeconds(1))
                .until(() -> true);

        assertThat(enableTimeToIdleCache.get("key").get()).isEqualTo("Hello World");
        assertThat(allowNullCache.get("key").get()).isEqualTo("Hello World");

        LocalDateTime enabledFinal = getExpirationTime(enableTimeToIdleCache.getNativeCache().get("key"));
        LocalDateTime disabledFinal = getExpirationTime(allowNullCache.getNativeCache().get("key"));

        assertThat(enableTimeToIdleCache.get("key").get()).isEqualTo("Hello World");
        assertThat(allowNullCache.get("key").get()).isEqualTo("Hello World");
        assertThat(enabledInitial).isBefore(enabledFinal);
        assertThat(disabledInitial).isEqualTo(disabledFinal);
    }

    private LocalDateTime getExpirationTime(Object object) throws Exception {
        return (LocalDateTime) getValueOfField(object, "expirationTime");
    }

    private Object getValueOfField(Object object, String fieldName) throws Exception {
        return ReflectionUtils.tryToReadFieldValue(object.getClass().getDeclaredField(fieldName), object)
                .getOrThrow(ex -> new IllegalArgumentException("Cannot load field" + fieldName + " of object " + object, ex));
    }

}
