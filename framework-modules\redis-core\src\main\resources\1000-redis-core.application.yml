destilink:
  fw:
    redis:
      core:
        command-timeouts:
          default-command-timeout: PT10S
          meta-command-timeout: PT20S
        socket-options:
          connect-timeout: PT5S
          keep-alive: true
        lettuce:
          share-native-connection: true
        keyspace-prefixes:
          application: ${spring.application.name}
          distributed: __distributed__
spring:
  data:
    redis:
      client-name: ${HOSTNAME:hey-sexy-service}
      repositories:
        enabled: false