package com.tui.destilink.framework.core.messaging.message.util;

import lombok.experimental.UtilityClass;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageHeaderAccessor;

import java.util.Map;
import java.util.UUID;

@UtilityClass
public class IdTsAwareMessageUtils {

    public static <T> Message<T> replacePayload(Message<T> message, T newPayload) {
        return buildMessage(newPayload, addHeadersIfAbsent(message.getHeaders(), Map.of()));
    }

    public static <T> Message<T> addHeader(Message<T> message, String headerName, Object headerValue) {
        return addHeaders(message, Map.of(headerName, headerValue));
    }

    public static <T> Message<T> addHeaders(Message<T> message, Map<String, Object> newHeaders) {
        return buildMessage(message.getPayload(), addHeaders(message.getHeaders(), newHeaders));
    }

    public static MessageHeaders addHeaders(MessageHeaders headers, Map<String, Object> newHeaders) {
        MessageHeaderAccessor accessor = new MessageHeaderAccessor();
        accessor.copyHeaders(headers);
        accessor.copyHeaders(newHeaders);
        return buildMessageHeaders(accessor.toMessageHeaders(), headers.getId(), headers.getTimestamp());
    }

    public static <T> Message<T> addHeaderIfAbsent(Message<T> message, String headerName, Object headerValue) {
        return addHeadersIfAbsent(message, Map.of(headerName, headerValue));
    }

    public static <T> Message<T> addHeadersIfAbsent(Message<T> message, Map<String, Object> newHeaders) {
        return buildMessage(message.getPayload(), addHeadersIfAbsent(message.getHeaders(), newHeaders));
    }

    public static MessageHeaders addHeadersIfAbsent(MessageHeaders headers, Map<String, Object> newHeaders) {
        MessageHeaderAccessor accessor = new MessageHeaderAccessor();
        accessor.copyHeaders(headers);
        accessor.copyHeadersIfAbsent(newHeaders);
        return buildMessageHeaders(accessor.toMessageHeaders(), headers.getId(), headers.getTimestamp());
    }

    private static <T> Message<T> buildMessage(T payload, MessageHeaders headers) {
        return new GenericMessage<>(payload, headers);
    }

    private static MessageHeaders buildMessageHeaders(Map<String, Object> headers, UUID id, Long timestamp) {
        return new IdTsAwareMessageHeaders(headers, id, timestamp);
    }
}
