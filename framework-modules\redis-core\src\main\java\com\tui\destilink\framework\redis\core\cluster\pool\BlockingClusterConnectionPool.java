package com.tui.destilink.framework.redis.core.cluster.pool;

import com.google.common.base.Supplier;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.event.Event;
import io.lettuce.core.support.ConnectionPoolSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.AbandonedConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.util.Assert;
import reactor.core.Disposable;

import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.concurrent.*;
import java.util.function.Consumer;

@Slf4j
public class BlockingClusterConnectionPool implements InitializingBean, DisposableBean {

    public static final Duration DEFAULT_MAX_WAIT_DURATION = Duration.ofSeconds(30);
    public static final int DEFAULT_POOL_MIN_IDLE = 2;
    public static final int DEFAULT_POOL_MAX_IDLE = 5;
    public static final int DEFAULT_POOL_MAX_TOTAL = 10;
    public static final int DEFAULT_POOL_BORROW_THREADS = 4;
    public static final int DEFAULT_POOL_RETURN_THREADS = 4;

    private final RedisClusterClient redisClient;
    private final String clientName;

    private final ExecutorService borrowConnectionExecutor = Executors.newFixedThreadPool(DEFAULT_POOL_BORROW_THREADS, new ThreadFactoryBuilder().setDaemon(true).setNameFormat("async-redis-acquire-pool-%d").build());
    private final ExecutorService returnConnectionExecutor = Executors.newFixedThreadPool(DEFAULT_POOL_RETURN_THREADS, new ThreadFactoryBuilder().setDaemon(true).setNameFormat("async-redis-return-pool-%d").build());

    private final GenericObjectPool<StatefulRedisClusterConnection<String, String>> connectionPool;

    private boolean initialized = false;

    public BlockingClusterConnectionPool(RedisClusterClient redisClient, RedisProperties properties) {
        this(redisClient, properties, defaultGenericObjectPoolConfig(), defaultAbandonedConfig());
    }

    public BlockingClusterConnectionPool(RedisClusterClient redisClient, RedisProperties properties, GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig, AbandonedConfig abandonedConfig) {
        this(redisClient, String.format("%s-%S", properties.getClientName(), "AsyncRedisClusterConnectionPool"), genericObjectPoolConfig, abandonedConfig);
    }

    protected BlockingClusterConnectionPool(RedisClusterClient redisClient, String clientName, GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig, AbandonedConfig abandonedConfig) {
        this.redisClient = redisClient;
        this.clientName = clientName;
        this.connectionPool = buildConnectionPool(genericObjectPoolConfig, abandonedConfig);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!initialized) {
            redisClient.refreshPartitions();
            connectionPool.preparePool();
            initialized = true;
        }
    }

    @Override
    public void destroy() {
        if (initialized) {
            initialized = false;
            connectionPool.close();
        }
    }

    public Disposable subscribeEventBus(Consumer<Event> consumer) {
        return redisClient.getResources().eventBus().get().subscribe(consumer);
    }

    public CompletableFuture<StatefulRedisClusterConnection<String, String>> getConnection() {
        return getConnection(DEFAULT_MAX_WAIT_DURATION);
    }


    public CompletableFuture<StatefulRedisClusterConnection<String, String>> getConnection(Duration maxWaitDuration) {
        assertInitialized();
        Assert.notNull(maxWaitDuration, "maxWaitDuration must not be null");

        CompletableFuture<StatefulRedisClusterConnection<String, String>> completed = new CompletableFuture<>();

        Future<?> future = null;
        try {
            future = borrowConnectionExecutor.submit(() -> {
                try {
                    completed.complete(connectionPool.borrowObject());
                } catch (InterruptedException ex) {
                    completed.completeExceptionally(createNoSuchElementException(maxWaitDuration));
                    Thread.currentThread().interrupt();
                } catch (NoSuchElementException ex) {
                    completed.completeExceptionally(ex);
                } catch (Exception ex) {
                    completed.completeExceptionally(createNoSuchElementException(maxWaitDuration, ex));
                }
            });
            if (maxWaitDuration.isNegative()) {
                future.get(DEFAULT_MAX_WAIT_DURATION.toMillis(), TimeUnit.MILLISECONDS);
            } else {
                future.get(maxWaitDuration.toMillis(), TimeUnit.MILLISECONDS);
            }
        } catch (TimeoutException | InterruptedException ex) {
            future.cancel(true);
            if (ex instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        } catch (Exception ex) {
            log.error("An unexpected error occurred while waiting for a connection from pool", ex);
            completed.completeExceptionally(createNoSuchElementException(maxWaitDuration, ex));
        }
        return completed;
    }

    public CompletableFuture<Void> returnConnection(StatefulRedisClusterConnection<String, String> connection) {
        return CompletableFuture.runAsync(() -> {
            try {
                assertInitialized();
                connectionPool.returnObject(connection);
            } catch (Exception ex) {
                log.error("Failed to return connection to pool", ex);
            }
        }, returnConnectionExecutor);
    }

    protected GenericObjectPool<StatefulRedisClusterConnection<String, String>> buildConnectionPool(GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig, AbandonedConfig abandonedConfig) {
        GenericObjectPool<StatefulRedisClusterConnection<String, String>> pool = ConnectionPoolSupport
                .createGenericObjectPool(connectionSupplier(), genericObjectPoolConfig, false);
        pool.setAbandonedConfig(abandonedConfig);
        return pool;
    }

    protected Supplier<StatefulRedisClusterConnection<String, String>> connectionSupplier() {
        return () -> {
            try {
                StatefulRedisClusterConnection<String, String> connection = redisClient.connect();
                connection.async().clientSetname(clientName).toCompletableFuture().join();
                return connection;
            } catch (RuntimeException ex) {
                log.error("Failed to initialize new connection with clientName {}", clientName, ex);
                throw ex;
            }
        };
    }

    public static GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> defaultGenericObjectPoolConfig() {
        return defaultGenericObjectPoolConfig(DEFAULT_POOL_MIN_IDLE, DEFAULT_POOL_MAX_IDLE, DEFAULT_POOL_MAX_TOTAL);
    }

    public static GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> defaultGenericObjectPoolConfig(int minIdle, int maxIdle, int maxTotal) {
        GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> config = new GenericObjectPoolConfig<>();
        config.setMaxIdle(maxIdle);
        config.setMinIdle(minIdle);
        config.setMaxTotal(maxTotal);
        config.setSoftMinEvictableIdleDuration(Duration.ofMinutes(5));
        config.setTimeBetweenEvictionRuns(Duration.ofMinutes(1));
        config.setMinEvictableIdleDuration(Duration.ofMinutes(5));
        config.setBlockWhenExhausted(true);
        config.setFairness(false);
        return config;
    }

    public static AbandonedConfig defaultAbandonedConfig() {
        AbandonedConfig abandonedConfig = new AbandonedConfig();
        abandonedConfig.setLogAbandoned(true);
        abandonedConfig.setRemoveAbandonedOnBorrow(false);
        abandonedConfig.setRemoveAbandonedOnMaintenance(true);
        abandonedConfig.setRemoveAbandonedTimeout(Duration.ofMinutes(5));
        return abandonedConfig;
    }

    protected void assertInitialized() {
        Assert.isTrue(initialized, "AsyncRedisClusterConnectionPool is not initialized");
    }

    protected NoSuchElementException createNoSuchElementException(Duration maxWaitDuration) {
        return new NoSuchElementException("Failed while waiting for idle object, maxWaitDuration=" + maxWaitDuration);
    }

    protected NoSuchElementException createNoSuchElementException(Duration maxWaitDuration, Throwable cause) {
        return new NoSuchElementException("Failed while waiting for idle object, maxWaitDuration=" + maxWaitDuration, cause);
    }
}
