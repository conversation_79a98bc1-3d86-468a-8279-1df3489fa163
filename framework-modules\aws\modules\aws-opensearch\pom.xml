<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.aws</groupId>
        <artifactId>aws</artifactId>
        <version>1.0.27</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>aws-opensearch</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-core</artifactId>
        </dependency>

        <!--we dont need the sniffer, since we are using opensearch from aws-->
        <!--https://www.elastic.co/de/blog/elasticsearch-sniffing-best-practices-what-when-why-how-->
        <dependency>
            <groupId>org.opensearch.client</groupId>
            <artifactId>spring-data-opensearch-starter</artifactId>
            <version>1.5.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.opensearch.client</groupId>
                    <artifactId>opensearch-rest-client-sniffer</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>