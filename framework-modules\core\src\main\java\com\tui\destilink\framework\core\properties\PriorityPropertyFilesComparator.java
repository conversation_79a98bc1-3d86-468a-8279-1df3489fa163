package com.tui.destilink.framework.core.properties;

import java.util.Comparator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PriorityPropertyFilesComparator implements Comparator<String> {

    private static final Pattern PRIORITY_REGEX_PATTERN = Pattern.compile("^(\\d+)-(\\S+)$");

    @Override
    public int compare(String o1, String o2) {
        // If both are equal
        if (o1.equalsIgnoreCase(o2)) {
            return o1.compareToIgnoreCase(o2);
        }

        final Matcher o1Matcher = PRIORITY_REGEX_PATTERN.matcher(o1);
        final Matcher o2Matcher = PRIORITY_REGEX_PATTERN.matcher(o2);

        int o1Priority;
        String o1Name;
        if (o1Matcher.matches()) {
            o1Priority = Integer.parseInt(o1Matcher.group(1));
            o1Name = o1Matcher.group(2);
        } else {
            o1Priority = Integer.MAX_VALUE;
            o1Name = o1;
        }

        int o2Priority;
        String o2Name;
        if (o2Matcher.matches()) {
            o2Priority = Integer.parseInt(o2Matcher.group(1));
            o2Name = o2Matcher.group(2);
        } else {
            o2Priority = Integer.MAX_VALUE;
            o2Name = o2;
        }

        if (o1Priority == o2Priority) {
            return String.CASE_INSENSITIVE_ORDER.compare(o1Name, o2Name);
        }

        return Integer.compare(o1Priority, o2Priority);
    }

}
