package com.tui.destilink.framework.core.test.env;

import com.tui.destilink.framework.core.env.FwEnvironmentName;
import com.tui.destilink.framework.core.test.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class})
class FwEnvironmentNameTest {

    @Autowired
    private FwEnvironmentName fwEnvironmentName;

    @Test
    void testTestSupportProperty() {
        assertThat(fwEnvironmentName).isEqualTo(FwEnvironmentName.TEST_SUPPORT);
    }
}
