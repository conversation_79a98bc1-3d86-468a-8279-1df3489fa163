<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-modules</artifactId>
        <version>1.0.27</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>openapi-generator</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <configuration>
                    <skip>${skipTests}</skip>
                    <addCompileSourceRoot>false</addCompileSourceRoot>
                    <addTestCompileSourceRoot>true</addTestCompileSourceRoot>
                </configuration>
                <executions>
                    <execution>
                        <id>spring-boot-server-1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/test/resources/openapi-spec-1.yml</inputSpec>
                            <output>${project.build.directory}/generated-test-sources/server-1</output>
                            <apiPackage>com.tui.destilink.openapi.generator.test.server.one.api</apiPackage>
                            <modelPackage>com.tui.destilink.openapi.generator.test.server.one.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>spring-boot-server-2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/test/resources/openapi-spec-2.yml</inputSpec>
                            <output>${project.build.directory}/generated-test-sources/server-2</output>
                            <apiPackage>com.tui.destilink.openapi.generator.test.server.two.api</apiPackage>
                            <modelPackage>com.tui.destilink.openapi.generator.test.server.two.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>webclient-1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/test/resources/openapi-spec-1.yml</inputSpec>
                            <output>${project.build.directory}/generated-test-sources/client-1</output>
                            <apiPackage>com.tui.destilink.openapi.generator.test.client.one.api</apiPackage>
                            <modelPackage>com.tui.destilink.openapi.generator.test.client.one.model</modelPackage>
                            <generatorName>java</generatorName>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <configOptions>
                                <library>webclient</library>
                                <dateLibrary>java8</dateLibrary>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>webclient-2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/test/resources/openapi-spec-2.yml</inputSpec>
                            <output>${project.build.directory}/generated-test-sources/client-2</output>
                            <apiPackage>com.tui.destilink.openapi.generator.test.client.two.api</apiPackage>
                            <modelPackage>com.tui.destilink.openapi.generator.test.client.two.model</modelPackage>
                            <generatorName>java</generatorName>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <configOptions>
                                <library>webclient</library>
                                <dateLibrary>java8</dateLibrary>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>openapi-generator</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>false</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>openapi-generator</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>false</skip.integrationTests>
            </properties>
        </profile>
    </profiles>

</project>