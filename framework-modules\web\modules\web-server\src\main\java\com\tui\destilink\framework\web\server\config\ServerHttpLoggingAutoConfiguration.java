package com.tui.destilink.framework.web.server.config;

import com.tui.destilink.framework.web.server.filter.LogbookFilterCustomizer;
import com.tui.destilink.framework.web.server.filter.LogbookFilterFactory;
import com.tui.destilink.framework.web.server.filter.LoggingContextCustomizer;
import com.tui.destilink.framework.web.server.filter.LoggingContextFilter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.zalando.logbook.servlet.LogbookFilter;

import java.util.*;

@AutoConfiguration
@Setter(onMethod_ = @Autowired(required = false))
public class ServerHttpLoggingAutoConfiguration {

    public static final int LOGGING_CONTEXT_ORDER = Ordered.HIGHEST_PRECEDENCE;
    public static final int LOGBOOK_ORDER = LOGGING_CONTEXT_ORDER + 5;

    private List<LoggingContextCustomizer> loggingContextCustomizers;
    private List<LogbookFilterCustomizer> logbookFilterCustomizers;

    @Bean
    @ConditionalOnMissingBean(value = LoggingContextFilter.class, parameterizedContainer = FilterRegistrationBean.class)
    FilterRegistrationBean<LoggingContextFilter> loggingContextFilter() {
        final LoggingContextFilter serverFilter = new LoggingContextFilter();
        final FilterRegistrationBean<LoggingContextFilter> registrationBean = new FilterRegistrationBean<>(serverFilter);

        final Map<String, String> additionalProperties = new HashMap<>();
        Optional.ofNullable(loggingContextCustomizers).orElse(Collections.emptyList()).forEach(customizer -> customizer.customize(additionalProperties));
        serverFilter.addAll(additionalProperties);

        registrationBean.setOrder(LOGGING_CONTEXT_ORDER);
        return registrationBean;
    }

    @Bean
    @ConditionalOnMissingBean(value = LogbookFilter.class, parameterizedContainer = FilterRegistrationBean.class)
    FilterRegistrationBean<LogbookFilter> logbookFilter() {
        final LogbookFilterFactory serverFilterFactory = new LogbookFilterFactory();
        Optional.ofNullable(logbookFilterCustomizers).orElse(Collections.emptyList()).forEach(customizer -> customizer.customize(serverFilterFactory));

        final LogbookFilter logbookFilter = serverFilterFactory.build();
        final FilterRegistrationBean<LogbookFilter> registrationBean = new FilterRegistrationBean<>(logbookFilter);
        registrationBean.setOrder(LOGBOOK_ORDER);
        return registrationBean;
    }
}