package com.tui.destilink.framework.redis.core.config;

import com.tui.destilink.framework.core.validation.UnresolvedPlaceholder;
import com.tui.destilink.framework.redis.core.util.RedisKeyPrefix;
import io.lettuce.core.protocol.CommandType;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import org.hibernate.validator.constraints.time.DurationMin;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.util.EnumMap;
import java.util.Map;
import java.util.Objects;

import static com.tui.destilink.framework.redis.core.config.RedisCoreProperties.PREFIX;

@Data
@Validated
@ConfigurationProperties(value = PREFIX, ignoreUnknownFields = false)
public class RedisCoreProperties {

    public static final String PREFIX = "destilink.fw.redis.core";

    @Valid
    @NotNull
    private RedisCoreProperties.CommandTimeouts commandTimeouts = new CommandTimeouts();

    @Valid
    @NotNull
    private SocketOptions socketOptions = new SocketOptions();

    @Valid
    @NotNull
    private Lettuce lettuce = new Lettuce();

    @Valid
    @NotNull
    private KeyspacePrefixes keyspacePrefixes = new KeyspacePrefixes();

    public CommandsTimeoutSource buildCommandsTimeoutSource() {
        return new CommandsTimeoutSource(this);
    }

    @AssertTrue(message = "Connect timeout must be smaller than meta- and default-command timeouts")
    protected boolean validateTimeouts() {
        return (commandTimeouts.metaCommandTimeout == null || socketOptions.connectTimeout.compareTo(commandTimeouts.metaCommandTimeout) < 0)
                && socketOptions.connectTimeout.compareTo(commandTimeouts.defaultCommandTimeout) < 0;
    }

    @Data
    @Validated
    public static class CommandTimeouts {
        @Nullable
        @DurationMin(millis = 10L)
        private Duration metaCommandTimeout = Duration.ofSeconds(20);
        @NotNull
        @DurationMin(millis = 10L)
        private Duration defaultCommandTimeout = Duration.ofSeconds(10);
        @NotNull
        private EnumMap<@NotNull CommandType, @NotNull Duration> overrideTimeouts = new EnumMap<>(CommandType.class);

        public void setOverrideTimeouts(Map<CommandType, Duration> overrideTimeouts) {
            Objects.requireNonNull(overrideTimeouts);
            this.overrideTimeouts = new EnumMap<>(overrideTimeouts);
        }
    }

    @Data
    @Validated
    public static class SocketOptions {
        @NotNull
        private Duration connectTimeout = Duration.ofSeconds(5);
        @NotNull
        private Boolean keepAlive = true;
    }

    @Data
    @Validated
    public static class Lettuce {
        @NotNull
        private Boolean shareNativeConnection = true;
    }

    @Data
    @Validated
    public static class KeyspacePrefixes {
        @NotBlank
        @Pattern(regexp = "^\\S*[^:]$")
        @UnresolvedPlaceholder
        private String application = "${spring.application.name}";
        @NotNull
        @Setter(AccessLevel.PRIVATE)
        private RedisKeyPrefix applicationPrefix = RedisKeyPrefix.of("${spring.application.name}");
        @NotBlank
        @Pattern(regexp = "^\\S*[^:]$")
        @UnresolvedPlaceholder
        private String distributed = "__distributed__";
        @NotNull
        @Setter(AccessLevel.PRIVATE)
        private RedisKeyPrefix distributedPrefix = RedisKeyPrefix.of(distributed);

        public void setApplication(String application) {
            this.application = application;
            this.applicationPrefix = RedisKeyPrefix.of(application);
        }

        public void setDistributed(String distributed) {
            this.distributed = distributed;
            this.distributedPrefix = RedisKeyPrefix.of(distributed);
        }
    }
}
