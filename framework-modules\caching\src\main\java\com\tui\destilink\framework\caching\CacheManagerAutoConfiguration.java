package com.tui.destilink.framework.caching;

import com.tui.destilink.framework.caching.cachemanager.CacheManagersProvider;
import com.tui.destilink.framework.caching.cachemanager.CompositeCacheManager;
import com.tui.destilink.framework.caching.config.CachingConfigProvider;
import com.tui.destilink.framework.caching.memory.InMemoryCacheAutoConfiguration;
import com.tui.destilink.framework.caching.redis.CachingRedisAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@AutoConfiguration
@AutoConfigureAfter({
        CachingAutoConfiguration.class,
        CachingRedisAutoConfiguration.class,
        InMemoryCacheAutoConfiguration.class})
@ConditionalOnBean(CachingConfigProvider.class)
public class CacheManagerAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean({CompositeCacheManager.class, CacheManager.class})
    public CompositeCacheManager compositeCacheManager(CachingConfigProvider cachingConfig, List<CacheManagersProvider<?>> providers) {
        final Map<String, CacheManager> cacheManagers = providers.stream().flatMap(p -> p.getCacheManagers().entrySet().stream())
                .collect(Collectors.toUnmodifiableMap(Map.Entry::getKey, Map.Entry::getValue));
        final long cacheManagerCount = providers.stream().mapToLong(p -> p.getCacheManagers().size()).sum();

        if (cacheManagers.size() != cacheManagerCount) {
            throw new IllegalStateException("Only one CacheManager per keyspace prefix is allowed");
        }

        // Check if all caches are associated with one cache manager
        final Set<String> configuredCaches = new HashSet<>(cachingConfig.getCaches().keySet());
        cacheManagers.values().stream()
                .flatMap(cm -> cm.getCacheNames().stream())
                .forEach(configuredCaches::remove);
        if (!configuredCaches.isEmpty()) {
            throw new IllegalStateException("Not all caches are associated with one cache manager " + configuredCaches);
        }

        final CacheManager defaultCacheManager = cacheManagers
                .getOrDefault(cachingConfig.getCacheDefaults().getKeyspacePrefix(), null);
        if (defaultCacheManager == null && cachingConfig.getCacheDefaults().getAllowRuntimeCacheCreation()) {
            throw new IllegalStateException("allowRuntimeCacheCreation is enabled but no CacheManager for the default keyspace prefix is available.");
        }

        CompositeCacheManager cacheManager;
        if (Boolean.TRUE.equals(cachingConfig.getCacheDefaults().getAllowRuntimeCacheCreation())) {
            cacheManager = new CompositeCacheManager(defaultCacheManager, cacheManagers);
        } else {
            cacheManager = new CompositeCacheManager(cacheManagers);
        }

        return cacheManager;
    }

}
