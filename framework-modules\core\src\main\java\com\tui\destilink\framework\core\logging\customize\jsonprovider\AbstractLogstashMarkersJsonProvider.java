package com.tui.destilink.framework.core.logging.customize.jsonprovider;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fasterxml.jackson.core.JsonGenerator;
import com.tui.destilink.framework.core.logging.customize.LogbackCustomizer;
import net.logstash.logback.composite.AbstractJsonProvider;
import net.logstash.logback.encoder.LogstashEncoder;
import net.logstash.logback.marker.LogstashMarker;
import org.slf4j.Marker;

import java.io.IOException;
import java.util.Iterator;
import java.util.List;

public abstract class AbstractLogstashMarkersJsonProvider extends AbstractJsonProvider<ILoggingEvent> implements LogbackCustomizer {

    protected void writeLogstashMarkerIfNecessary(JsonGenerator generator, List<Marker> markers) throws IOException {
        if (markers != null) {
            for (Marker marker : markers) {
                writeLogstashMarkerIfNecessary(generator, marker);
            }
        }
    }

    protected void writeLogstashMarkerIfNecessary(JsonGenerator generator, Marker marker) throws IOException {
        if (marker != null) {
            if (isLogstashMarker(marker)) {
                ((LogstashMarker) marker).writeTo(generator);
            }

            if (marker.hasReferences()) {
                for (Iterator<?> i = marker.iterator(); i.hasNext(); ) {
                    Marker next = (Marker) i.next();
                    writeLogstashMarkerIfNecessary(generator, next);
                }
            }
        }
    }

    protected boolean isLogstashMarker(Marker marker) {
        return marker instanceof LogstashMarker;
    }

    @Override
    public void customize(LogstashEncoder encoder) {
        encoder.addProvider(this);
    }
}
