data-sources:
  test1:
    url: jdbc:aws-wrapper:postgresql://some.endpoint:5432/postgres
    username: someuser
    password: somepassword
  test2:
    url: *********************************************
    username: someuser
    password: somepassword
    driver-class-name: software.amazon.jdbc.Driver
    hikari:
      exception-override-class-name: some.exception.to.not.override
      data-source-properties:
        wrapperDialect: aurora-pg
        wrapperPlugins: ""
        wrapperDriverName: "other driver name"
        monitoring-connectTimeout: 20
        monitoring-socketTimeout: 20
  test3:
    url: *********************************************
    username: someuser
    password: somepassword
  test4-ro:
    url: jdbc:aws-wrapper:postgresql://xxxxxxxx.cluster-ro-xxxxx.eu-central-1.rds.amazonaws.com:5432/postgres
    username: someuser
    password: somepassword
    hikari:
      max-lifetime: 1000
  test5-ro:
    url: jdbc:aws-wrapper:postgresql://xxxxxxxx.cluster-ro-xxxxx.eu-central-1.rds.amazonaws.com:5432/postgres?SomeOtherProperty=hello-world
    username: someuser
    password: somepassword
    hikari:
      maxLifetime: 2000
  test6-ro:
    url: ****************************************************************************************
    username: someuser
    password: somepassword
    driver-class-name: software.amazon.jdbc.Driver
  proxy-ro:
    url: **********************************************************************************************************
    username: someuser
    password: somepassword
    driver-class-name: software.amazon.jdbc.Driver