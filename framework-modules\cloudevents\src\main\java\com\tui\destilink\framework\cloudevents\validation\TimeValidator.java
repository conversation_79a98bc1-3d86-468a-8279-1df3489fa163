package com.tui.destilink.framework.cloudevents.validation;

import com.google.auto.service.AutoService;
import com.tui.destilink.framework.cloudevents.validation.util.AttributeValidatorUtils;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.validator.CloudEventValidator;

/**
 * Time is an optional field according to the CloudEvent Spec,
 * but it is required for TUI. The class is loaded via the
 */
@SuppressWarnings("unused")
@AutoService(CloudEventValidator.class)
public class TimeValidator implements CloudEventValidator {

    @Override
    public void validate(CloudEvent cloudEvent) {
        if (cloudEvent.getTime() == null) {
            throw AttributeValidatorUtils.INSTANCE.missingException("time");
        }
    }
}
