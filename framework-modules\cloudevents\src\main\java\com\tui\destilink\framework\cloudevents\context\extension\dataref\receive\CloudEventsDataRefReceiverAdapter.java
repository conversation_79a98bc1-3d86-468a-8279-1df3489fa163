package com.tui.destilink.framework.cloudevents.context.extension.dataref.receive;

import io.cloudevents.CloudEventData;
import lombok.Getter;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.lang.NonNull;

import java.net.URI;
import java.util.concurrent.CompletableFuture;

@Getter
public abstract class CloudEventsDataRefReceiverAdapter {

    private final boolean isAsync;

    protected CloudEventsDataRefReceiverAdapter(boolean isAsync) {
        this.isAsync = isAsync;
    }

    public abstract boolean supports(@NonNull URI dataRef);

    public ImmutablePair<CloudEventData, String> receive(@NonNull URI dataRef) {
        if (isAsync) {
            throw new UnsupportedOperationException("This adapter only supports async operations");
        }
        return doReceive(dataRef);
    }

    public CompletableFuture<ImmutablePair<CloudEventData, String>> receiveAsync(@NonNull URI dataRef) {
        if (!isAsync) {
            throw new UnsupportedOperationException("This adapter only supports non-async operations");
        }
        return doReceiveAsync(dataRef);
    }

    protected ImmutablePair<CloudEventData, String> doReceive(@NonNull URI dataRef) {
        throw new UnsupportedOperationException();
    }

    protected CompletableFuture<ImmutablePair<CloudEventData, String>> doReceiveAsync(@NonNull URI dataRef) {
        throw new UnsupportedOperationException();
    }
}
