package com.tui.destilink.framework.file.download.impl;

import com.tui.destilink.framework.file.download.AbstractFileDownloadServiceFactory;
import com.tui.destilink.framework.file.download.DownloadConfiguration;
import com.tui.destilink.framework.file.download.DownloadType;
import com.tui.destilink.framework.file.storage.StorageService;
import org.springframework.core.io.support.ResourcePatternResolver;

public class SftpFileDownloadServiceFactory implements AbstractFileDownloadServiceFactory {

    @Override
    public boolean canHandle(DownloadType type) {
        return DownloadType.SFTP == type;
    }

    @Override
    public AbstractFileDownloadService create(DownloadConfiguration configuration,
                                              StorageService storageService,
                                              ResourcePatternResolver resourcePatternResolver) {
        return new SftpFileDownloadService(configuration.getHost(),
                configuration.getPort(),
                configuration.getUsername(),
                configuration.getPassword(),
                storageService);
    }

}
