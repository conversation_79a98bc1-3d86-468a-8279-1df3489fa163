package com.tui.destilink.framework.core.env;

import lombok.Getter;

@Getter
public enum FwEnvironmentName {
    TEST_SUPPORT("test-support"),
    LOCAL("local"),
    DEV("dev"),
    TEST("test"),
    RELEASE("release"),
    PROD("prod");

    private final String value;

    FwEnvironmentName(String value) {
        this.value = value;
    }

    public static FwEnvironmentName of(String value) {
        for (FwEnvironmentName env : values()) {
            if (env.value.equalsIgnoreCase(value)) {
                return env;
            }
        }
        throw new IllegalArgumentException("Invalid environment value: " + value);
    }
}
