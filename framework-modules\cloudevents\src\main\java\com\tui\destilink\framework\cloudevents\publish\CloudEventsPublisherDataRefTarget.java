package com.tui.destilink.framework.cloudevents.publish;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.net.URI;
import java.util.Map;

@Getter
@SuperBuilder(toBuilder = true)
public abstract class CloudEventsPublisherDataRefTarget {

    public static final Map<String, String> FILE_EXTENSION_BY_CONTENT_TYPE = Map.of("application/json", ".json");

    private final URI baseUri;

    public URI generateDataRef(String filename, String dataContentType) {
        String extension = FILE_EXTENSION_BY_CONTENT_TYPE.getOrDefault(dataContentType, "");
        return baseUri.resolve(filename + extension);
    }
}
