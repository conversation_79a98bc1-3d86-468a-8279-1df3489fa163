package com.tui.destilink.framework.core.logging.customize;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import net.logstash.logback.encoder.LogstashEncoder;

/**
 * Requires @AutoService(LogbackCustomizer.class) annotation on the implementing class
 */
public interface LogbackCustomizer {

    default void customize(LoggerContext context) {
    }

    default void customize(Appender<ILoggingEvent> appender) {
    }

    default void customize(LogstashEncoder encoder) {
    }

}
