package com.tui.destilink.framework.redis.core.future;

import com.tui.destilink.framework.core.util.FutureUtils;
import io.lettuce.core.LettuceFutures;
import io.lettuce.core.RedisFuture;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

public class FailedRedisFuture<T> extends CompletableFuture<T> implements RedisFuture<T> {

    private final Throwable exception;

    public FailedRedisFuture(Throwable ex) {
        this.exception = FutureUtils.unwrapCompletionException(ex);
        completeExceptionally(this.exception);
    }

    @Override
    public String getError() {
        return exception.getMessage();
    }

    @Override
    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        return LettuceFutures.awaitAll(timeout, unit, this);
    }
}
