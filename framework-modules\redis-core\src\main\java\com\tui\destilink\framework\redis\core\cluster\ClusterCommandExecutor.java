package com.tui.destilink.framework.redis.core.cluster;

import com.tui.destilink.framework.core.util.FutureUtils;
import com.tui.destilink.framework.redis.core.cluster.pool.BlockingClusterConnectionPool;
import com.tui.destilink.framework.redis.core.cluster.pool.CycleClusterConnectionPool;
import com.tui.destilink.framework.redis.core.future.FailedRedisFuture;
import io.lettuce.core.RedisCommandExecutionException;
import io.lettuce.core.RedisException;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulConnection;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.cluster.PartitionSelectorException;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.SlotHash;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.async.AsyncExecutions;
import io.lettuce.core.cluster.api.async.NodeSelectionAsyncCommands;
import io.lettuce.core.cluster.api.async.RedisClusterAsyncCommands;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

@Slf4j
public class ClusterCommandExecutor implements InitializingBean, DisposableBean {

    public static final Duration WAIT_FOR_REPLICATION_TIMEOUT_DEFAULT = Duration.ofSeconds(5);

    private final BlockingClusterConnectionPool blockingConnectionPool;
    private final CycleClusterConnectionPool cycleClusterConnectionPool;

    private boolean initialized = false;

    public ClusterCommandExecutor(RedisClusterClient redisClusterClient, RedisProperties properties) {
        this.blockingConnectionPool = new BlockingClusterConnectionPool(redisClusterClient, properties);
        this.cycleClusterConnectionPool = new CycleClusterConnectionPool(redisClusterClient);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!initialized) {
            blockingConnectionPool.afterPropertiesSet();
            cycleClusterConnectionPool.afterPropertiesSet();
            initialized = true;
        }
    }

    @Override
    public void destroy() {
        if (initialized) {
            blockingConnectionPool.destroy();
            cycleClusterConnectionPool.destroy();
        }
    }

    public <T> CompletableFuture<List<T>> executeOnAllNodes(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenCompose(clusterConnection -> function.apply(clusterConnection.async().all().commands()));
    }

    public <T> CompletableFuture<List<T>> executeOnAllUpstream(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenCompose(clusterConnection -> function.apply(clusterConnection.async().upstream().commands()));
    }

    public <T> CompletableFuture<List<T>> executeOnAllReplicas(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenCompose(clusterConnection -> function.apply(clusterConnection.async().replicas().commands()));
    }

    public <T> CompletableFuture<List<T>> executeOnAllFiltered(Predicate<RedisClusterNode> filter, boolean dynamic, Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenCompose(clusterConnection -> function.apply(clusterConnection.async().nodes(filter, dynamic).commands()));
    }

    public <T> CompletableFuture<RedisFuture<T>> execute(Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenApply(c -> function.apply(c.async()));
    }

    public <T> CompletableFuture<RedisFuture<T>> executeOnNode(String key, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return executeOnNode(SlotHash.getSlot(key), function);
    }

    public <T> CompletableFuture<RedisFuture<T>> executeOnNode(int slot, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return cycleClusterConnectionPool.getClusterConnection().thenCompose(
                clusterConnection -> getNodeConnection(clusterConnection, slot)
                        .thenApply(nodeConnection -> function.apply(nodeConnection.async())));
    }

    public <T> CompletableFuture<RedisFuture<T>> executeSyncedOnNode(String key, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return executeSyncedOnNode(SlotHash.getSlot(key), function);
    }

    public <T> CompletableFuture<RedisFuture<T>> executeSyncedOnNode(int slot, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return blockingConnectionPool.getConnection().thenCompose(clusterConnection ->
                getNodeConnection(clusterConnection, slot)
                        .thenCompose(nodeConnection -> {
                            int replicas = clusterConnection.async().replicas(n -> n.hasSlot(slot)).size();
                            return waitForReplication(function.apply(nodeConnection.async()), nodeConnection, replicas);
                        }).whenComplete((v, t) -> blockingConnectionPool.returnConnection(clusterConnection)));
    }

    public <T> CompletableFuture<RedisFuture<T>> executeInPipelineOnNode(int slot, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return blockingConnectionPool.getConnection().thenCompose(clusterConnection ->
                getNodeConnection(clusterConnection, slot).thenCompose(nodeConnection ->
                                executeInPipeline(nodeConnection, nodeConnection.async(), function))
                        .whenComplete((v, t) -> blockingConnectionPool.returnConnection(clusterConnection)));
    }

    public <T> CompletableFuture<RedisFuture<T>> executeInPipelineSyncedOnNode(int slot, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        Function<RedisClusterAsyncCommands<String, String>, CompletableFuture<RedisFuture<T>>> functionWrapper = c -> {
            RedisFuture<T> redisFuture = function.apply(c);
            RedisFuture<Long> wait = c.waitForReplication(1, 5000);
            return wait.toCompletableFuture().handle((l, t) -> {
                if (t != null) {
                    log.error("Failed to wait for replication", t);
                }
                return redisFuture;
            });
        };
        return blockingConnectionPool.getConnection().thenCompose(clusterConnection ->
                getNodeConnection(clusterConnection, slot).thenCompose(nodeConnection ->
                        executeInPipelineC(nodeConnection, nodeConnection.async(), functionWrapper)
                                .whenComplete((v, t) -> blockingConnectionPool.returnConnection(clusterConnection))));
    }

    public CompletableFuture<Void> executeInPipeline(Consumer<RedisClusterAsyncCommands<String, String>> consumer) {
        return blockingConnectionPool.getConnection().thenCompose(connection -> {
            try {
                connection.setAutoFlushCommands(false);
                consumer.accept(connection.async());
            } catch (RedisException ex) {
                return CompletableFuture.failedFuture(ex);
            } catch (Exception ex) {
                return CompletableFuture.failedFuture(new RedisCommandExecutionException("Failed handle consumer in pipelined execution", ex));
            } finally {
                connection.flushCommands();
                connection.setAutoFlushCommands(true);
                blockingConnectionPool.returnConnection(connection);
            }
            return FutureUtils.COMPLETED_FUTURE;
        });
    }

    public <T> CompletableFuture<RedisFuture<T>> executeInPipeline(Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return blockingConnectionPool.getConnection().thenCompose(connection -> executeInPipeline(connection, connection.async(), function)
                .whenComplete((v, r) -> blockingConnectionPool.returnConnection(connection)));
    }

    protected <T> CompletableFuture<RedisFuture<T>> executeInPipeline(RedisClusterNode node, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return blockingConnectionPool.getConnection().thenCompose(clusterConnection -> getNodeConnection(clusterConnection, node)
                .thenCompose(nodeConnection -> executeInPipeline(nodeConnection, nodeConnection.async(), function))
                .whenComplete((v, r) -> blockingConnectionPool.returnConnection(clusterConnection)));
    }

    protected <T> CompletableFuture<RedisFuture<T>> executeInPipelineC(StatefulConnection<String, String> connection, RedisClusterAsyncCommands<String, String> asyncCommands, Function<RedisClusterAsyncCommands<String, String>, CompletableFuture<RedisFuture<T>>> function) {
        return FutureUtils.COMPLETED_FUTURE.thenCompose(v -> {
            CompletableFuture<RedisFuture<T>> result;
            try {
                connection.setAutoFlushCommands(false);
                result = function.apply(asyncCommands);
            } catch (RedisException ex) {
                result = new FailedRedisFuture<>(ex);
            } catch (Exception ex) {
                result = new FailedRedisFuture<>(new RedisCommandExecutionException("Failed handle function in pipelined execution", ex));
            } finally {
                connection.flushCommands();
                connection.setAutoFlushCommands(true);
            }
            return result;
        });
    }

    protected <T> CompletableFuture<RedisFuture<T>> executeInPipeline(StatefulConnection<String, String> connection, RedisClusterAsyncCommands<String, String> asyncCommands, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        return FutureUtils.COMPLETED_FUTURE.thenCompose(v -> {
            RedisFuture<T> result;
            try {
                connection.setAutoFlushCommands(false);
                result = function.apply(asyncCommands);
            } catch (RedisException ex) {
                result = new FailedRedisFuture<>(ex);
            } catch (Exception ex) {
                result = new FailedRedisFuture<>(new RedisCommandExecutionException("Failed handle function in pipelined execution", ex));
            } finally {
                connection.flushCommands();
                connection.setAutoFlushCommands(true);
            }
            return CompletableFuture.completedFuture(result);
        });
    }

    protected CompletableFuture<StatefulRedisConnection<String, String>> getNodeConnection(StatefulRedisClusterConnection<String, String> clusterConnection, int slot) {
        return CompletableFuture.completedFuture(clusterConnection).thenCompose(c -> {
            RedisClusterNode node = c.getPartitions().getMasterBySlot(slot);
            if (node == null) {
                return CompletableFuture.failedFuture(new PartitionSelectorException("Cannot find master node for slot " + slot, clusterConnection.getPartitions()));
            }
            return getNodeConnection(clusterConnection, node);
        });
    }

    protected CompletableFuture<StatefulRedisConnection<String, String>> getNodeConnection(StatefulRedisClusterConnection<String, String> clusterConnection, RedisClusterNode node) {
        return CompletableFuture.completedFuture(node).thenCompose(n -> {
            RedisURI uri = node.getUri();
            if (uri != null) {
                return getNodeConnection(clusterConnection, uri.getHost(), uri.getPort());
            }
            return getNodeConnection(clusterConnection, node.getNodeId());
        });
    }

    protected CompletableFuture<StatefulRedisConnection<String, String>> getNodeConnection(StatefulRedisClusterConnection<String, String> clusterConnection, String nodeId) {
        return clusterConnection.getConnectionAsync(nodeId);
    }

    protected CompletableFuture<StatefulRedisConnection<String, String>> getNodeConnection(StatefulRedisClusterConnection<String, String> clusterConnection, String host, int port) {
        return clusterConnection.getConnectionAsync(host, port);
    }

    protected <T> CompletableFuture<RedisFuture<T>> waitForReplication(RedisFuture<T> redisFuture, StatefulRedisConnection<String, String> connection, int replicas) {
        return redisFuture.toCompletableFuture()
                .thenCompose(v -> waitForReplication(connection, replicas))
                .handle((ignoredV, ignoredT) -> redisFuture);
    }

    protected CompletableFuture<Void> waitForReplication(StatefulRedisConnection<String, String> connection, int replicas) {
        if (replicas < 1) {
            return FutureUtils.COMPLETED_FUTURE;
        }
        return connection.async().waitForReplication(replicas, WAIT_FOR_REPLICATION_TIMEOUT_DEFAULT.toMillis())
                .toCompletableFuture().handle((r, t) -> {
                    if (t != null) {
                        Throwable throwable = FutureUtils.unwrapCompletionException(t);
                        log.error("Failed to wait {}ms for replication to {} replicas", WAIT_FOR_REPLICATION_TIMEOUT_DEFAULT.toMillis(), replicas, throwable);
                    } else if (r != null && r != replicas) {
                        log.warn("Only {} replicas of expected {} where replicated in {}ms", r, replicas, WAIT_FOR_REPLICATION_TIMEOUT_DEFAULT.toMillis());
                    }
                    return null;
                });
    }
}
