package com.tui.destilink.framework.resilience.retry;

import java.util.LinkedHashMap;
import java.util.Map;

public class CompositeRetryPredicate implements RetryPredicate {

    private static final Map<String, RetryPredicate> retryPredicates = new LinkedHashMap<>();

    @Override
    public boolean test(Throwable t) {
        return retryPredicates.values().stream().anyMatch(predicate -> predicate.test(t));
    }

    public static synchronized void registerRetryPredicate(RetryPredicate retryPredicate) {
        retryPredicates.put(retryPredicate.getClass().getName(), retryPredicate);
    }
}
