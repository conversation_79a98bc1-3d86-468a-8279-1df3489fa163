package com.tui.destilink.framework.caching.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.caching.CachingAutoConfiguration;
import com.tui.destilink.framework.caching.cachemanager.CacheManagersProvider;
import com.tui.destilink.framework.caching.condition.ConditionalOnCacheBackend;
import com.tui.destilink.framework.caching.config.CachingConfigProvider;
import com.tui.destilink.framework.caching.config.CachingProperties;
import com.tui.destilink.framework.caching.config.CachingProperties.BackendBindingType;
import com.tui.destilink.framework.redis.core.converter.GenericKeyConverter;
import com.tui.destilink.framework.redis.core.serializer.RedisGzipSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.BatchStrategies;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheManager.RedisCacheManagerBuilder;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
@AutoConfiguration
@AutoConfigureAfter(CachingAutoConfiguration.class)
@AutoConfigureBefore(RedisAutoConfiguration.class)
@ConditionalOnClass({RedisOperations.class, LettuceConnectionFactory.class})
@ConditionalOnCacheBackend(BackendBindingType.REDIS)
@ConditionalOnBean(CachingConfigProvider.class)
public class CachingRedisAutoConfiguration {

    @Bean
    public CacheManagersProvider<RedisCacheManager> redisCacheManagersProvider(
            CachingConfigProvider cachingConfig, RedisConnectionFactory connectionFactory, ObjectMapper objectMapper,
            ObjectProvider<RedisCacheManagerBuilderCustomizer> builderCustomizers) {

        final Set<String> keyspacePrefixes = cachingConfig.getKeyspacePrefixesForBackendType(BackendBindingType.REDIS);
        final Map<String, RedisCacheManager> cacheManagers = HashMap.newHashMap(keyspacePrefixes.size());

        keyspacePrefixes.forEach(p -> {
            RedisCacheManagerBuilder builder = buildCacheManagerBuilder(p, cachingConfig, connectionFactory, objectMapper);
            builderCustomizers.orderedStream().forEach(customizer -> customizer.customize(builder));
            cacheManagers.put(p, builder.build());
        });

        return new CacheManagersProvider<>(cacheManagers);
    }

    private RedisCacheManagerBuilder buildCacheManagerBuilder(
            String keyspacePrefix, CachingConfigProvider cachingConfig,
            RedisConnectionFactory connectionFactory, ObjectMapper objectMapper) {
        final Map<String, CachingProperties.CacheEntry> caches = cachingConfig
                .getCachesForKeyspacePrefix(keyspacePrefix);

        validateCacheCount(keyspacePrefix, caches, cachingConfig.getCacheDefaults());

        final RedisCacheManagerBuilder builder = RedisCacheManager
                .builder(RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory, BatchStrategies.scan(100)))
                .cacheDefaults(buildRedisDefaultCacheConfig(cachingConfig.getCacheDefaults(), objectMapper))
                .allowCreateOnMissingCache(cachingConfig.getCacheDefaults().getAllowRuntimeCacheCreation())
                .enableStatistics();

        caches.forEach((key, value) -> {
            log.debug("Initialize redis cache '{}' with config {}", key, value);
            builder.withCacheConfiguration(key, buildRedisCacheConfig(value, objectMapper));
        });

        return builder;
    }

    private RedisCacheConfiguration buildRedisDefaultCacheConfig(CachingProperties.CacheDefaults cacheDefaultProps, ObjectMapper objectMapper) {
        return buildRedisCacheConfig(cacheDefaultProps.getKeyspacePrefix(), cacheDefaultProps.getTtl(), cacheDefaultProps.getEnableTimeToIdle(), cacheDefaultProps.getAllowNullValues(), cacheDefaultProps.getUseCompression(), null, objectMapper);
    }

    private RedisCacheConfiguration buildRedisCacheConfig(CachingProperties.CacheEntry cachingProps, ObjectMapper objectMapper) {
        return buildRedisCacheConfig(cachingProps.getKeyspacePrefix(), cachingProps.getTtl(), cachingProps.getEnableTimeToIdle(), cachingProps.getAllowNullValues(), cachingProps.getUseCompression(), cachingProps.getType(), objectMapper);
    }

    private RedisCacheConfiguration buildRedisCacheConfig(String keyspacePrefix, Duration ttl, boolean enableTimeToIdle, boolean allowCacheNullValues, boolean useCompression, Class<?> type, ObjectMapper objectMapper) {
        RedisSerializer<?> serializer = buildRedisSerializer(useCompression, type, objectMapper);

        RedisCacheConfiguration redisConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(ttl)
                .prefixCacheNameWith(keyspacePrefix)
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(serializer));
        // TimeToIdle is disabled by default
        if (enableTimeToIdle) {
            redisConfig = redisConfig.enableTimeToIdle();
        }
        // Caching null values is enabled by default
        if (!allowCacheNullValues) {
            redisConfig = redisConfig.disableCachingNullValues();
        }
        redisConfig.addCacheKeyConverter(new GenericKeyConverter());
        return redisConfig;
    }

    private <T> RedisSerializer<?> buildRedisSerializer(boolean useCompression, Class<T> type, ObjectMapper objectMapper) {
        RedisSerializer<?> serializer;
        if (type != null) {
            serializer = new Jackson2JsonRedisSerializer<>(objectMapper, type);
        } else {
            serializer = new JdkSerializationRedisSerializer();
        }
        if (useCompression) {
            return new RedisGzipSerializer<>(serializer);
        }
        return serializer;
    }

    private void validateCacheCount(String keyspacePrefix, Map<String, CachingProperties.CacheEntry> caches, CachingProperties.CacheDefaults defaults) {
        final boolean isDefaultKeyspacePrefix = defaults.getKeyspacePrefix().equals(keyspacePrefix);

        if (caches.isEmpty()) {
            // Empty caches are only allowed for the default keyspace prefix if runtime cache creation is allowed
            if (isDefaultKeyspacePrefix && Boolean.TRUE.equals(defaults.getAllowRuntimeCacheCreation())) {
                return;
            }
            throw new IllegalStateException("Did not find any caches for keyspace prefix " + keyspacePrefix);
        }
    }

}
