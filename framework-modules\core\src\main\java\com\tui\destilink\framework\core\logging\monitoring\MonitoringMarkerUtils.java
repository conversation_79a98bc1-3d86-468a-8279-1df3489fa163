package com.tui.destilink.framework.core.logging.monitoring;

import com.fasterxml.jackson.annotation.JsonValue;
import com.tui.destilink.framework.core.logging.monitoring.MonitoringMoodData.Mood;
import com.tui.destilink.framework.core.logging.monitoring.issue.Issue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.UtilityClass;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;

/**
 * A utility class for creating LogstashMarkers with a specific field name and associated object.
 * These markers can be used in logging frameworks like Logback to add additional context or
 * metadata to log entries, which can be useful for monitoring or analysis purposes.
 */
@UtilityClass
public class MonitoringMarkerUtils {

    @RequiredArgsConstructor
    class StringWrapper implements Issue {

        @Getter(onMethod_ = @JsonValue)
        private final String value;
    }

    @Getter
    @RequiredArgsConstructor
    enum MonitoringMarker {
        /**
         * A marker for log entries which can be used for monitoring or analysis purposes
         */
        MONITORING("monitoring");

        private final String fieldName;
    }

    public static LogstashMarker appendForMonitoringMood(Mood mood) {
        return appendForMonitoring(CompositeMonitoringData.builder().value(monitoringMoodData(mood)).build());
    }

    public static LogstashMarker appendForMonitoringIssue(String issue) {
        return appendForMonitoring(CompositeMonitoringData.builder().value(monitoringIssueData(issue)).build());
    }

    public static LogstashMarker appendForMonitoringIssue(String issue, Mood mood) {
        return appendForMonitoring(CompositeMonitoringData.builder().value(monitoringIssueData(issue)).value(monitoringMoodData(mood)).build());
    }

    public static LogstashMarker appendForMonitoringIssue(Issue issue) {
        return appendForMonitoring(CompositeMonitoringData.builder().value(monitoringIssueData(issue)).build());
    }

    public static LogstashMarker appendForMonitoringIssue(Issue issue, Mood mood) {
        return appendForMonitoring(CompositeMonitoringData.builder().value(monitoringIssueData(issue)).value(monitoringMoodData(mood)).build());
    }

    public static LogstashMarker appendForMonitoring(CompositeMonitoringData monitoringData) {
        return Markers.append(MonitoringMarker.MONITORING.getFieldName(), monitoringData);
    }

    private static MonitoringMoodData monitoringMoodData(Mood mood) {
        return new MonitoringMoodData(mood);
    }

    private static MonitoringIssueData monitoringIssueData(String issue) {
        return new MonitoringIssueData(new StringWrapper(issue));
    }

    private static MonitoringIssueData monitoringIssueData(Issue issue) {
        return new MonitoringIssueData(issue);
    }
}