package com.tui.destilink.framework.core.util;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.experimental.UtilityClass;

import java.util.Set;

@UtilityClass
public class ValidationUtils {

    public static final Validator DEFAULT_VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    public static <T> void validateObjectThrowing(T object) throws ConstraintViolationException {
        Set<ConstraintViolation<T>> violations = DEFAULT_VALIDATOR.validate(object);
        if (violations.isEmpty()) {
            return;
        }
        throw new ConstraintViolationException(violations);
    }

    public static <T> Set<ConstraintViolation<T>> validateObject(T object) {
        return DEFAULT_VALIDATOR.validate(object);
    }
}
