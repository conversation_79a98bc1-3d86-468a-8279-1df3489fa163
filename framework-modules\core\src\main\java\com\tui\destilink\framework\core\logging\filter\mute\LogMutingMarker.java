package com.tui.destilink.framework.core.logging.filter.mute;

import com.fasterxml.jackson.core.JsonGenerator;
import lombok.EqualsAndHashCode;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;
import net.logstash.logback.marker.SingleFieldAppendingMarker;
import org.slf4j.event.Level;

import java.io.IOException;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
public class LogMutingMarker extends SingleFieldAppendingMarker {

    public static final String MARKER_NAME = SingleFieldAppendingMarker.MARKER_NAME_PREFIX + "LOG_MUTING";

    public static final String FIELD_NAME = "logback.mute.level";

    private final String level;

    protected LogMutingMarker(String level) {
        super(MARKER_NAME, FIELD_NAME);
        this.level = level;
    }

    @Override
    protected void writeFieldValue(JsonGenerator jsonGenerator) throws IOException {
        jsonGenerator.writeObject(level);
    }

    @Override
    protected Object getFieldValue() {
        return level;
    }

    public static LogstashMarker of(Level level) {
        return Optional.ofNullable(level)
                .map(Level::name)
                .map(LogMutingMarker::new)
                .map(LogstashMarker.class::cast)
                .orElse(Markers.empty());
    }

    public static LogstashMarker of(java.util.logging.Level level) {
        return Optional.ofNullable(level)
                .map(java.util.logging.Level::getName)
                .map(LogMutingMarker::new)
                .map(LogstashMarker.class::cast)
                .orElse(Markers.empty());
    }

    public static LogstashMarker of(ch.qos.logback.classic.Level level) {
        return Optional.ofNullable(level)
                .map(ch.qos.logback.classic.Level::toString)
                .map(LogMutingMarker::new)
                .map(LogstashMarker.class::cast)
                .orElse(Markers.empty());
    }
}