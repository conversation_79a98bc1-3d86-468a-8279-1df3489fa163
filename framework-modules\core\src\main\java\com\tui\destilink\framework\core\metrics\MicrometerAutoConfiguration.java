package com.tui.destilink.framework.core.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;

import java.time.Instant;
import java.util.Optional;

@AutoConfiguration
@AutoConfigureBefore(MetricsAutoConfiguration.class)
public class MicrometerAutoConfiguration {

    private static final String UNKNOWN = "UNKNOWN";

    @Value("${DD_ENV:UNKNOWN_DD_ENV}")
    private String ddEnv;

    @Value("${DD_SERVICE:UNKNOWN_DD_SERVICE}")
    private String ddService;

    @Value("${DD_VERSION:UNKNOWN_DD_VERSION}")
    private String ddVersion;

    @Value("${HOSTNAME:#{null}}")
    private String hostname;

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> meterRegistryCustomizerDatadog() {
        return registry -> registry.config()
                .commonTags("env", Optional.ofNullable(ddEnv).orElse(UNKNOWN))
                .commonTags("service", Optional.ofNullable(ddService).orElse(UNKNOWN))
                .commonTags("version", Optional.ofNullable(ddVersion).orElse(UNKNOWN));
    }

    @Bean
    @ConditionalOnBean(BuildProperties.class)
    public MeterRegistryCustomizer<MeterRegistry> meterRegistryCustomizerBuildProperties(BuildProperties buildProperties) {
        return registry -> registry.config()
                .commonTags("build.group", Optional.ofNullable(buildProperties.getGroup()).orElse(UNKNOWN))
                .commonTags("build.artifact", Optional.ofNullable(buildProperties.getArtifact()).orElse(UNKNOWN))
                .commonTags("build.version", Optional.ofNullable(buildProperties.getVersion()).orElse(UNKNOWN))
                .commonTags("build.name", Optional.ofNullable(buildProperties.getName()).orElse(UNKNOWN))
                .commonTags("build.time", Optional.ofNullable(buildProperties.getTime()).map(Instant::toString).orElse(UNKNOWN));
    }

    @Bean
    @Profile("kubernetes")
    public MeterRegistryCustomizer<MeterRegistry> meterRegistryCustomizerK8s() {
        return registry -> registry.config()
                .commonTags("pod_name", Optional.ofNullable(hostname).orElse(UNKNOWN));
    }
}
