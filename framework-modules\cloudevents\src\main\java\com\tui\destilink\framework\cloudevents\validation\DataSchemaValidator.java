package com.tui.destilink.framework.cloudevents.validation;

import com.google.auto.service.AutoService;
import com.tui.destilink.framework.cloudevents.context.attribute.dataschema.CloudEventsDataSchema;
import com.tui.destilink.framework.cloudevents.validation.util.AttributeValidatorUtils;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.validator.CloudEventValidator;

import java.net.URI;

/**
 * DataSchema is an optional field according to the CloudEvent Spec
 */
@SuppressWarnings("unused")
@AutoService(CloudEventValidator.class)
public class DataSchemaValidator implements CloudEventValidator {

    @Override
    public void validate(CloudEvent cloudEvent) {
        URI dataSchemaUri = cloudEvent.getDataSchema();
        if (dataSchemaUri != null) {
            try {
                CloudEventsDataSchema.validate(dataSchemaUri);
            } catch (Exception ex) {
                throw AttributeValidatorUtils.INSTANCE.invalidValueException("dataschema", dataSchemaUri, ex.getMessage());
            }
        }
    }
}
