package com.tui.destilink.framework.core.logging.customize;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.OutputStreamAppender;
import ch.qos.logback.core.spi.AppenderAttachable;
import net.logstash.logback.encoder.LogstashEncoder;
import org.slf4j.ILoggerFactory;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.util.Assert;

import java.util.ServiceLoader;
import java.util.Set;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

public class LogbackCustomizerApplicationListener implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {

    private Set<LogbackCustomizer> customizers;

    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        ILoggerFactory loggerFactorySpi = LoggerFactory.getILoggerFactory();
        Assert.isInstanceOf(LoggerContext.class, loggerFactorySpi);

        customizeLoggerContext(getCustomizers(), (LoggerContext) loggerFactorySpi);
        customizeAppender(getCustomizers(), (LoggerContext) loggerFactorySpi);
        customizeLogstashEncoder(getCustomizers(), (LoggerContext) loggerFactorySpi);
    }

    private synchronized Set<LogbackCustomizer> getCustomizers() {
        if (customizers == null) {
            customizers = ServiceLoader.load(LogbackCustomizer.class).stream()
                    .map(ServiceLoader.Provider::get)
                    .collect(Collectors.toSet());
        }
        return customizers;
    }

    private void customizeLoggerContext(Set<LogbackCustomizer> customizers, LoggerContext context) {
        customizers.forEach(c -> c.customize(context));
    }

    private void customizeAppender(Set<LogbackCustomizer> customizers, LoggerContext context) {
        context.getLoggerList().stream()
                .flatMap(this::getAppenderStream)
                .forEach(a -> customizers.forEach(c -> c.customize(a)));
    }

    private void customizeLogstashEncoder(Set<LogbackCustomizer> customizers, LoggerContext context) {
        context.getLoggerList().stream()
                .flatMap(this::getAppenderStream)
                .filter(OutputStreamAppender.class::isInstance)
                .map(OutputStreamAppender.class::cast)
                .filter(a -> a.getEncoder() instanceof LogstashEncoder)
                .map(a -> (LogstashEncoder) a.getEncoder())
                .forEach(e -> customizers.forEach(c -> c.customize(e)));
    }

    @SuppressWarnings("unchecked")
    private Stream<Appender<ILoggingEvent>> getAppenderStream(AppenderAttachable<ILoggingEvent> attachable) {
        return StreamSupport.stream(
                        Spliterators.spliteratorUnknownSize(attachable.iteratorForAppenders(), Spliterator.ORDERED),
                        false)
                .flatMap(a -> {
                    if (a instanceof AppenderAttachable<?> aa) {
                        // Extract nested appenders
                        return getAppenderStream((AppenderAttachable<ILoggingEvent>) aa);
                    }
                    return Stream.of(a);
                });
    }
}
