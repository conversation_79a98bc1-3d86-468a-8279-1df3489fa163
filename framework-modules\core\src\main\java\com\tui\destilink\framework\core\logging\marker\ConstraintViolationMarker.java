package com.tui.destilink.framework.core.logging.marker;

import com.fasterxml.jackson.core.JsonGenerator;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import net.logstash.logback.marker.SingleFieldAppendingMarker;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
public class ConstraintViolationMarker extends SingleFieldAppendingMarker {

    public static final String MARKER_NAME = SingleFieldAppendingMarker.MARKER_NAME_PREFIX + "CONSTRAINT_VIOLATION";

    public static final String FIELD_NAME = "constraintViolations";

    private final transient Collection<HashMap<String, Object>> messageValues;

    protected ConstraintViolationMarker(@NotNull List<HashMap<String, Object>> messageValues) {
        super(MARKER_NAME, FIELD_NAME);
        this.messageValues = messageValues;
    }

    @Override
    protected void writeFieldValue(JsonGenerator generator) throws IOException {
        generator.writeObject(messageValues);
    }

    @Override
    public Object getFieldValue() {
        return messageValues;
    }

    public static ConstraintViolationMarker of(ConstraintViolationException exception) {
        if (exception == null) {
            return build(Set.of());
        }
        return build(exception.getConstraintViolations());
    }

    public static <T> ConstraintViolationMarker of(ConstraintViolation<T> violation) {
        if (violation == null) {
            return of(Set.of());
        }
        return of(Set.of(violation));
    }

    public static <T> ConstraintViolationMarker of(Set<ConstraintViolation<T>> violations) {
        if (violations == null) {
            return build(Set.of());
        }
        return build(violations.stream().map(v -> (ConstraintViolation<?>) v).collect(Collectors.toSet()));
    }

    private static ConstraintViolationMarker build(Set<ConstraintViolation<?>> violations) {
        if (violations == null) {
            violations = Set.of();
        }
        List<HashMap<String, Object>> result = new ArrayList<>(violations.size());
        for (ConstraintViolation<?> violation : violations) {
            HashMap<String, Object> messageValue = new HashMap<>();
            messageValue.put("propertyPath", violation.getPropertyPath().toString());
            messageValue.put("message", violation.getMessage());
            result.add(messageValue);
        }
        return new ConstraintViolationMarker(result);
    }
}
