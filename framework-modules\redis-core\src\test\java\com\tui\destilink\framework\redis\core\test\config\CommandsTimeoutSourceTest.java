package com.tui.destilink.framework.redis.core.test.config;

import com.tui.destilink.framework.core.util.ValidationUtils;
import com.tui.destilink.framework.redis.core.config.CommandsTimeoutSource;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.test.support.core.util.NonContextTestEnvUtils;
import io.lettuce.core.protocol.Command;
import io.lettuce.core.protocol.CommandType;
import jakarta.validation.ConstraintViolation;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.tui.destilink.framework.redis.core.config.CommandsTimeoutSource.META_COMMAND_TYPES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

class CommandsTimeoutSourceTest {

    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(10);
    private static final Duration META_TIMEOUT = Duration.ofSeconds(20);

    @Test
    void testPropertyDefaults() {
        RedisCoreProperties props = bindRedisCoreProperties();
        validateAssertEmpty(props);
        assertThat(props.getCommandTimeouts()).isNotNull();
        RedisCoreProperties.CommandTimeouts commandTimeouts = props.getCommandTimeouts();
        assertThat(commandTimeouts).isNotNull();
        assertThat(commandTimeouts.getDefaultCommandTimeout()).isEqualTo(Duration.ofSeconds(10));
        assertThat(commandTimeouts.getMetaCommandTimeout()).isEqualTo(Duration.ofSeconds(20));
        assertThat(commandTimeouts.getOverrideTimeouts()).isEmpty();
    }

    @Test
    void testValidateFailed() {
        RedisCoreProperties props = bindRedisCoreProperties();

        Set<ConstraintViolation<RedisCoreProperties>> result = validate(props);
        assertThat(result).isEmpty();

        ReflectionTestUtils.setField(props.getCommandTimeouts(), "defaultCommandTimeout", null);
        result = validate(props);
        assertThat(result).hasSize(1);

        ReflectionTestUtils.setField(props.getCommandTimeouts(), "metaCommandTimeout", null);
        result = validate(props);
        assertThat(result).hasSize(1);

        // Reset - Null not allowed
        assertThrows(NullPointerException.class, () -> bindRedisCoreProperties().getCommandTimeouts().setOverrideTimeouts(null));

        props = bindRedisCoreProperties();
        HashMap<CommandType, Duration> overrides = new HashMap<>();
        overrides.put(CommandType.UNLINK, null);
        props.getCommandTimeouts().setOverrideTimeouts(overrides);
        result = validate(props);
        assertThat(result).hasSize(1);

        overrides.put(CommandType.GET, null);
        props.getCommandTimeouts().setOverrideTimeouts(overrides);
        result = validate(props);
        assertThat(result).hasSize(2);

        overrides.put(CommandType.UNLINK, DEFAULT_TIMEOUT);
        overrides.put(CommandType.GET, DEFAULT_TIMEOUT);
        props.getCommandTimeouts().setOverrideTimeouts(overrides);
        result = validate(props);
        assertThat(result).isEmpty();
    }

    @Test
    void testWithMeta() {
        RedisCoreProperties props = bindRedisCoreProperties();
        ReflectionTestUtils.setField(props.getCommandTimeouts(), "defaultCommandTimeout", DEFAULT_TIMEOUT);
        ReflectionTestUtils.setField(props.getCommandTimeouts(), "metaCommandTimeout", META_TIMEOUT);
        validateAssertEmpty(props);

        CommandsTimeoutSource source = props.buildCommandsTimeoutSource();
        assertThat(source).isNotNull();
        assertThat(source.getHighestTimeout()).isEqualTo(META_TIMEOUT);
        for (CommandType ct : META_COMMAND_TYPES) {
            assertThat(source.getTimeout(new Command<>(ct, null, null))).isEqualTo(META_TIMEOUT.toMillis());
        }

        int count = 0;
        for (CommandType ct : CommandType.values()) {
            if (!META_COMMAND_TYPES.contains(ct)) {
                count++;
                assertThat(source.getTimeout(new Command<>(ct, null, null))).isEqualTo(DEFAULT_TIMEOUT.toMillis());
            }
        }
        assertThat(count)
                .isPositive()
                .isEqualTo(CommandType.values().length - META_COMMAND_TYPES.size());
    }

    @Test
    void testWithOutMeta() {
        RedisCoreProperties props = bindRedisCoreProperties();
        ReflectionTestUtils.setField(props.getCommandTimeouts(), "defaultCommandTimeout", DEFAULT_TIMEOUT);
        ReflectionTestUtils.setField(props.getCommandTimeouts(), "metaCommandTimeout", null);
        validateAssertEmpty(props);

        CommandsTimeoutSource source = props.buildCommandsTimeoutSource();
        assertThat(source.getHighestTimeout()).isEqualTo(DEFAULT_TIMEOUT);
        for (CommandType ct : CommandType.values()) {
            assertThat(source.getTimeout(new Command<>(ct, null, null))).isEqualTo(DEFAULT_TIMEOUT.toMillis());
        }
    }

    @Test
    void testOverrides() {
        RedisCoreProperties props = bindRedisCoreProperties();
        Map<CommandType, Duration> overrides = new HashMap<>();
        overrides.put(CommandType.FLUSHDB, Duration.ofSeconds(30));
        overrides.put(CommandType.KEYS, Duration.ofSeconds(40));
        overrides.put(CommandType.GET, Duration.ofSeconds(50));
        props.getCommandTimeouts().setOverrideTimeouts(overrides);
        props.getCommandTimeouts().setDefaultCommandTimeout(DEFAULT_TIMEOUT);
        props.getCommandTimeouts().setMetaCommandTimeout(META_TIMEOUT);
        validateAssertEmpty(props);

        CommandsTimeoutSource source = props.buildCommandsTimeoutSource();
        assertThat(source.getTimeout(new Command<>(CommandType.FLUSHDB, null, null))).isEqualTo(Duration.ofSeconds(30).toMillis());
        assertThat(source.getTimeout(new Command<>(CommandType.KEYS, null, null))).isEqualTo(Duration.ofSeconds(40).toMillis());
        assertThat(source.getTimeout(new Command<>(CommandType.GET, null, null))).isEqualTo(Duration.ofSeconds(50).toMillis());
        assertThat(source.getHighestTimeout()).isEqualTo(Duration.ofSeconds(50));
        for (CommandType ct : CommandType.values()) {
            if (overrides.containsKey(ct)) {
                continue;
            }
            if (META_COMMAND_TYPES.contains(ct)) {
                assertThat(source.getTimeout(new Command<>(ct, null, null))).isEqualTo(META_TIMEOUT.toMillis());
            } else {
                assertThat(source.getTimeout(new Command<>(ct, null, null))).isEqualTo(DEFAULT_TIMEOUT.toMillis());
            }
        }
    }

    void validateAssertEmpty(RedisCoreProperties props) {
        assertThat(validate(props)).isEmpty();
    }

    Set<ConstraintViolation<RedisCoreProperties>> validate(RedisCoreProperties props) {
        return ValidationUtils.validateObject(props);
    }

    RedisCoreProperties bindRedisCoreProperties() {
        RedisCoreProperties props = NonContextTestEnvUtils.bindProperties(RedisCoreProperties.PREFIX, RedisCoreProperties.class, false);
        props.getKeyspacePrefixes().setApplication("dummy-to-replace-placeholder");
        return props;
    }
}
