package com.tui.destilink.framework.cloudevents.aws.sns.publish;

import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherContext;
import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.slf4j.MDC;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;

import java.util.Map;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Getter
@SuperBuilder(toBuilder = true)
public class SnsCloudEventsPublisherContext<T> extends CloudEventsPublisherContext<T, SnsCloudEventsPublishResult> {
    private final Arn topicArn;
    @Builder.Default
    private final Function<T, String> messageGroupId = t -> MDC.get(TripsContextConstants.BUSINESS_PROCESS_ID);
    @Builder.Default
    private final Function<T, String> deduplicationId = t -> UUID.randomUUID().toString();
    @Builder.Default
    private final BiConsumer<T, Map<String, MessageAttributeValue>> messageAttributes = (t, m) -> {
    };
}
