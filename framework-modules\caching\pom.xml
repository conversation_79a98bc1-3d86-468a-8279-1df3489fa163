<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-modules</artifactId>
        <version>1.0.27</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>caching</artifactId>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>redis-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>caching</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>false</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>caching</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>false</skip.integrationTests>
            </properties>
        </profile>
    </profiles>

</project>
