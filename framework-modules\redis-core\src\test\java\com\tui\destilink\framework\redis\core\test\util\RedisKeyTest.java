package com.tui.destilink.framework.redis.core.test.util;

import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.redis.core.util.RedisKey;
import com.tui.destilink.framework.redis.core.util.RedisKeyPrefix;
import com.tui.destilink.framework.test.support.core.util.NonContextTestEnvUtils;
import org.junit.jupiter.api.Test;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class RedisKeyTest {

    private static final String TEST_JOIN = "one:two:three";
    private static final String[] TEST_ARR = {"one", "two", "three"};
    private static final List<String> TEST_LIST = Arrays.asList(TEST_ARR);

    private static final String TEST_STRING_KEY = TEST_JOIN + ":" + TEST_JOIN + ":{" + TEST_JOIN + "}:{" + TEST_JOIN + "}";
    private static final String TEST_STRING_PREFIX = TEST_STRING_KEY + ":";

    private static RedisKeyPrefix buildTestPrefix() {
        return RedisKeyPrefix.of()
                .append(TEST_ARR)
                .append(TEST_LIST)
                .appendHashTag(TEST_ARR)
                .appendHashTag(TEST_LIST);
    }

    @Test
    void testProperties() {
        RedisCoreProperties props = NonContextTestEnvUtils.bindProperties(RedisCoreProperties.PREFIX, RedisCoreProperties.class, false);
        assertThat(props.getKeyspacePrefixes().getApplicationPrefix().getPrefix())
                .isEqualTo("${spring.application.name}:");
        assertThat(props.getKeyspacePrefixes().getDistributedPrefix().getPrefix())
                .isEqualTo("__distributed__:");
    }

    @Test
    void testPropertiesWithProfile() {
        ConfigurableEnvironment env = NonContextTestEnvUtils.loadTestEnvironment(Set.of("ks"));
        RedisCoreProperties props = NonContextTestEnvUtils.bindProperties(env, RedisCoreProperties.PREFIX, RedisCoreProperties.class);
        assertThat(props.getKeyspacePrefixes().getApplicationPrefix().getPrefix())
                .isEqualTo("destilink:application:");
        assertThat(props.getKeyspacePrefixes().getDistributedPrefix().getPrefix())
                .isEqualTo("distributed:lalala:");
    }

    @Test
    void testPrefixOf() {
        RedisKeyPrefix empty = RedisKeyPrefix.of();
        RedisKeyPrefix arr = RedisKeyPrefix.of(TEST_ARR);
        RedisKeyPrefix list = RedisKeyPrefix.of(TEST_LIST);

        assertThat(empty.getPrefix()).isEmpty();
        assertThat(arr.getPrefix())
                .isEqualTo(list.getPrefix())
                .isEqualTo(TEST_JOIN + ":");
    }

    @Test
    void testPrefixAppend() {
        RedisKeyPrefix prefix = buildTestPrefix();
        assertThat(prefix.getPrefix()).isEqualTo(TEST_STRING_PREFIX);
    }

    @Test
    void testKey() {
        RedisKeyPrefix prefix = buildTestPrefix();
        RedisKey key = prefix.toRedisKey();
        assertThat(key.getKey()).isEqualTo(TEST_STRING_KEY);

        key = key
                .append(TEST_ARR)
                .appendHashTag(TEST_ARR);
        assertThat(key.getKey()).isEqualTo(TEST_STRING_PREFIX + TEST_JOIN + ":{" + TEST_JOIN + "}");
        prefix = key.toRedisKeyPrefix();
        assertThat(prefix.getPrefix()).isEqualTo(TEST_STRING_PREFIX + TEST_JOIN + ":{" + TEST_JOIN + "}:");
    }
}
