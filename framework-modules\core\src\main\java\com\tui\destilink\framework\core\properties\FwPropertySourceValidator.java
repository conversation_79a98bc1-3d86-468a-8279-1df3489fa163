package com.tui.destilink.framework.core.properties;

import org.springframework.boot.DefaultPropertiesPropertySource;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.PropertySource;
import org.springframework.util.Assert;

public class FwPropertySourceValidator implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {

    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        PropertySource<?> fwPropertySource = event.getEnvironment()
                .getPropertySources().get(DefaultPropertiesPropertySource.NAME);

        Assert.isInstanceOf(FwCompositePropertySource.class, fwPropertySource);
    }

}
