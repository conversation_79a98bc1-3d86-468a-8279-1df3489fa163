{"properties": [{"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.monitoringConnectTimeout", "type": "java.lang.Integer", "defaultValue": 10}, {"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.monitoringSocketTimeout", "type": "java.lang.Integer", "defaultValue": 10}, {"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.wrapperDriverName", "type": "java.lang.String", "defaultValue": "PostgreSQL JDBC Driver"}, {"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.wrapperPlugins", "type": "java.lang.String", "defaultValue": "driverMetaData,auroraConnectionTracker,failover,efm2"}, {"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.failoverClusterTopologyRefreshRateMs", "type": "java.lang.Long", "defaultValue": 100}, {"name": "destilink.fw.aws.rds.aurora.postgresql.hikari.default-properties.failoverWriterReconnectIntervalMs", "type": "java.lang.Long", "defaultValue": 1000}]}