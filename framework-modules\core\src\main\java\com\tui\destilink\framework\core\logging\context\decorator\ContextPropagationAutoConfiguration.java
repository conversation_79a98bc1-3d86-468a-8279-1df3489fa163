package com.tui.destilink.framework.core.logging.context.decorator;

import io.micrometer.context.ContextRegistry;
import io.micrometer.context.integration.Slf4jThreadLocalAccessor;
import jakarta.annotation.PostConstruct;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;

@Slf4j
@AutoConfiguration
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ContextPropagationAutoConfiguration {

    @PostConstruct
    private void automaticContextPropagation() {
        ContextRegistry.getInstance()
                .registerThreadLocalAccessor(new Slf4jThreadLocalAccessor());
    }

    @Configuration
    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    @ConditionalOnClass({Hooks.class, ContextRegistry.class})
    static class ReactiveContextPropagationConfiguration {

        @PostConstruct
        private void automaticContextPropagation() {
            // https://micrometer.io/docs/contextPropagation
            // https://spring.io/blog/2023/03/30/context-propagation-with-project-reactor-3-unified-bridging-between-reactive
            // https://medium.com/javarevisited/demystifying-context-propagation-in-spring-web-flux-with-threadlocalaccessor-contextregistry-mdc-c3740e0b990e
            Hooks.enableAutomaticContextPropagation();
        }

    }
}
