package com.tui.destilink.framework.trips.core.logging;

import lombok.Value;
import lombok.experimental.NonFinal;
import lombok.experimental.SuperBuilder;

@Value
@NonFinal
@SuperBuilder(toBuilder = true)
public class HttpContext {

    // mandatory
    String url;
    String urlDetails; // can be auto-filled by datadog
    String method; // use HttpMethod constants
    Integer statusCode;

    // recommended
    HttpContextTypeEnum type;

}
