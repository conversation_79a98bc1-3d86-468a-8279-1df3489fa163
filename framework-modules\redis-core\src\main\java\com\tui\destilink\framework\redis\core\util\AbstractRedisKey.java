package com.tui.destilink.framework.redis.core.util;

import io.lettuce.core.codec.StringCodec;
import lombok.AccessLevel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collection;

@Getter(AccessLevel.PROTECTED)
public abstract class AbstractRedisKey<S extends AbstractRedisKey<S>> {

    protected static final StringCodec CODEC = StringCodec.UTF8;

    private final String value;

    protected AbstractRedisKey(String value) {
        this.value = value == null ? "" : value;
    }

    public S append(String... elements) {
        return append(Arrays.asList(elements));
    }

    public S append(Collection<String> elements) {
        String join = RedisKeyUtils.join(elements);
        return newObject(append(join));
    }

    public S appendHashTag(String... elements) {
        return appendHashTag(Arrays.asList(elements));
    }

    public S appendHashTag(Collection<String> elements) {
        String join = RedisKeyUtils.joinHashTag(elements);
        return newObject(append(join));
    }

    protected abstract S newObject(String value);

    private String append(String toAppend) {
        if (!value.isEmpty()) {
            return RedisKeyUtils.join(value, toAppend);
        }
        return toAppend;
    }

    @Override
    public String toString() {
        return value;
    }
}
