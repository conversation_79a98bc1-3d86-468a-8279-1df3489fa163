package com.tui.destilink.framework.web.client.webclient;

import com.tui.destilink.framework.trips.core.logging.MDCPrefixWrapper;
import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import com.tui.destilink.framework.web.client.httpclient.HttpClientConfig;
import com.tui.destilink.framework.web.core.logging.tracing.BookhubProcessor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockserver.client.MockServerClient;
import org.mockserver.matchers.Times;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.HttpStatusCode;
import org.mockserver.model.MediaType;
import org.mockserver.springtest.MockServerTest;
import org.mockserver.verify.VerificationTimes;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;

import java.net.URI;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest
@ActiveProfiles({ "it", "kubernetes" })
@EnableAutoConfiguration
@MockServerTest
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WebClientHttpLoggingIT {

    private final WebClientFactory webClientFactory;
    private MockServerClient mockServerClient;

    @Value("${mockServerPort}")
    private Integer port;

    private Stream<WebClient.Builder> webClientBuilderProvider() {
        return Stream.of(
            webClientFactory.buildWebClientBuilder(new HttpClientConfig()),
            webClientFactory.buildWebClientBuilder(NettyHttpClientFactory.getHttpClient(new HttpClientConfig()))
        );
    }

    @ParameterizedTest
    @MethodSource("webClientBuilderProvider")
    void givenHttpRequestShouldBeLoggedProperly(WebClient.Builder builder, CapturedOutput output) {
        mockServerClient.when(
                            HttpRequest.request()
                                       .withMethod(HttpMethod.POST.name())
                                       .withPath("/api.*"),
                            Times.exactly(1))
                        .respond(
                            HttpResponse.response()
                                        .withStatusCode(HttpStatusCode.OK_200.code())
                                        .withContentType(MediaType.PLAIN_TEXT_UTF_8)
                                        .withBody("pong"));

        MDCPrefixWrapper.put(BookhubProcessor.CONTEXT_MULTIREQUESTID, "1234");
        MDC.put(TripsContextConstants.EVENT_ID, "456");

        builder.build().post()
               .uri(URI.create(String.format("Http://localhost:%d/api", port)))
               .bodyValue("ping")
               .retrieve()
               .bodyToMono(String.class)
               .block();

        //When logging request and response, these will be expected twice in ThreadContext
        assertThat(StringUtils.countMatches(output.getOut(), "\"contextMap.multiRequestId\":\"1234\"")).isEqualTo(2);
        assertThat(StringUtils.countMatches(output.getOut(), "\"trips.eventId\":")).isEqualTo(2);

        //When logging request headers
        assertThat(output.getOut()).containsOnlyOnce("bookhub-multi-request-id: 1234").containsOnlyOnce("tui-event-id: 456");
        assertThat(StringUtils.countMatches(output.getOut(), "x-datadog-parent-id:")).isEqualTo(1);
        assertThat(StringUtils.countMatches(output.getOut(), "x-datadog-sampling-priority:")).isEqualTo(1);
        assertThat(StringUtils.countMatches(output.getOut(), "x-datadog-tags:")).isEqualTo(1);
        assertThat(StringUtils.countMatches(output.getOut(), "x-datadog-trace-id:")).isEqualTo(1);

        mockServerClient.verify(HttpRequest.request()
                                           .withMethod("POST")
                                           .withPath("/api.*")
                                           .withHeader("bookhub-multi-request-id", "1234")
                                           .withHeader("tui-event-id", "456"),
                                VerificationTimes.once());
    }
}