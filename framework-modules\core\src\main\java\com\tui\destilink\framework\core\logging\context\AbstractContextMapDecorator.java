package com.tui.destilink.framework.core.logging.context;

import com.tui.destilink.framework.core.logging.context.decorator.AbstractContextDecorator;

public abstract class AbstractContextMapDecorator<S extends AbstractContextDecorator<S>> extends AbstractContextDecorator<S> {

    public static final String PREFIX = "contextMap.";

    protected AbstractContextMapDecorator() {
        super(PREFIX);
    }

    protected AbstractContextMapDecorator(String contextMapSuffix) {
        super(PREFIX + contextMapSuffix);
    }
}
