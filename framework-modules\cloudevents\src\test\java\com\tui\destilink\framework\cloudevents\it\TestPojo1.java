package com.tui.destilink.framework.cloudevents.it;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;

import java.util.UUID;

@Getter
@Builder(toBuilder = true)
public class TestPojo1 {

    public static final TestPojo1 VALID_TEST_DATA = TestPojo1.builder()
            .helloWorld11("I am helloWorld1_1")
            .helloWorld12("I am helloWorld1_2")
            .helloWorld13("I am helloWorld1_3")
            .build();

    private final String id = UUID.randomUUID().toString();
    @NotNull
    private final String helloWorld11;
    private final String helloWorld12;
    private final String helloWorld13;
    private final int i;
}
