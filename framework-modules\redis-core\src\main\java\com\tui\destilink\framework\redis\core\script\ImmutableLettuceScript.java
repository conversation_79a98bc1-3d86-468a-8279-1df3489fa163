package com.tui.destilink.framework.redis.core.script;

import io.lettuce.core.ScriptOutputType;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.connection.lettuce.LettuceConverters;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

@Getter
public class ImmutableLettuceScript<T> implements RedisScript<T> {

    private static final ResourceLoader DEFAULT_RESOURCE_LOADER = new DefaultResourceLoader();

    private final @Nullable Class<T> resultType;
    private final @NotNull ScriptOutputType outputType;

    private final @NotNull String sha1;
    private final @NotNull String script;

    public ImmutableLettuceScript(@Nullable Class<T> resultType, ScriptOutputType outputType, String sha1, String script) {
        Assert.notNull(outputType, "OutputType must not be null");
        Assert.notNull(sha1, "Sha1 must not be null");
        Assert.notNull(script, "Script must not be null");
        this.resultType = resultType;
        this.outputType = outputType;
        this.sha1 = sha1;
        this.script = script;
    }

    @Override
    public String getSha1() {
        return sha1;
    }

    @Override
    public Class<T> getResultType() {
        return resultType;
    }

    @Override
    public String getScriptAsString() {
        return script;
    }

    public static <T> ImmutableLettuceScript<T> loadFromClasspath(String location) {
        return of(DEFAULT_RESOURCE_LOADER.getResource(location));
    }

    public static <T> ImmutableLettuceScript<T> loadFromClasspath(String location, Class<T> resultType) {
        return of(DEFAULT_RESOURCE_LOADER.getResource(location), resultType);
    }

    public static <T> ImmutableLettuceScript<T> of(RedisScript<T> source) {
        return new ImmutableLettuceScript<>(source.getResultType(), scriptOutputType(source), source.getSha1(), source.getScriptAsString());
    }

    public static <T> ImmutableLettuceScript<T> of(String script) {
        return of(RedisScript.of(script));
    }

    public static <T> ImmutableLettuceScript<T> of(String script, Class<T> resultType) {
        return of(RedisScript.of(script, resultType));
    }

    public static <T> ImmutableLettuceScript<T> of(Resource resource) {
        return of(RedisScript.of(resource));
    }

    public static <T> ImmutableLettuceScript<T> of(Resource resource, Class<T> resultType) {
        Assert.notNull(resource, "Resource must not be null");
        Assert.notNull(resultType, "ResultType must not be null");
        return of(RedisScript.of(resource, resultType));
    }

    private static ScriptOutputType scriptOutputType(RedisScript<?> source) {
        return scriptOutputType(source.getResultType());
    }

    private static <L> ScriptOutputType scriptOutputType(Class<L> resultTypeClazz) {
        return LettuceConverters.toScriptOutputType(ReturnType.fromJavaType(resultTypeClazz));
    }
}
