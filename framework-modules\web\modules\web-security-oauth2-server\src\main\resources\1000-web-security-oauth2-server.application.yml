spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${tui.passport.issuer-uri}
destilink:
  fw:
    web:
      security:
        oauth2:
          user-info:
            enabled: false
            type: com.tui.destilink.framework.web.security.oauth2.server.userinfo.model.BasicUserInfo
            use-fallback: false
            cache-enabled: true
            user-info-cache-name: "oauth2-jwt-user-info"
            ttl: PT5M
            enable-time-to-idle: true
            use-compression: true