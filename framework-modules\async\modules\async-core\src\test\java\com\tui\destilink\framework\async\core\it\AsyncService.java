package com.tui.destilink.framework.async.core.it;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
public class AsyncService {
    public static final String EXCEPTION_MESSAGE = "Exception wanted by test";

    @Async
    public CompletableFuture<Boolean> runAsync() {
        return CompletableFuture.completedFuture(Thread.currentThread().isVirtual());
    }

    @Async // methods with void return cannot propagate exception to caller via get()
    public void exceptionAsync() {
        throw new IllegalArgumentException(EXCEPTION_MESSAGE);
    }
}
