destilink:
  fw:
    caching:
      caches:
        cache1: { }
        cache2: { }
        cache3:
          keyspace-prefix: "cache3:"
          ttl: PT5S
        enableTimeToIdleCache:
          enable-time-to-idle: true
        allowNullValuesCache:
          allow-null-values: true
      backends:
        in-memory:
          instances:
            - { }
            - keyspace-prefix: "cache3:"
              cleanup-cron: "* * * * * *"
      backend-bindings:
        - type: in_memory
        - keyspace-prefix: "cache3:"
          type: in_memory