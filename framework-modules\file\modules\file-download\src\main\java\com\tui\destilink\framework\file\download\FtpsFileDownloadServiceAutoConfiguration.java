package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.download.impl.FtpsFileDownloadServiceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.integration.ftp.session.DefaultFtpsSessionFactory;

@RequiredArgsConstructor
@AutoConfiguration
@ConditionalOnClass(DefaultFtpsSessionFactory.class)
public class FtpsFileDownloadServiceAutoConfiguration {

    @Bean
    AbstractFileDownloadServiceFactory ftpsFileDownloadServiceFactory() {
        return new FtpsFileDownloadServiceFactory();
    }
}
