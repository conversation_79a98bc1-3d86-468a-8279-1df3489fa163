package com.tui.destilink.framework.file.download;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;

@Data
@EqualsAndHashCode(of = {"filename"})
@Builder
public class FileInfo {
    private String filename;
    private OffsetDateTime createTime;
    private OffsetDateTime lastModifiedTime;
    private OffsetDateTime accessTime;
    private Long fileSize;
}
