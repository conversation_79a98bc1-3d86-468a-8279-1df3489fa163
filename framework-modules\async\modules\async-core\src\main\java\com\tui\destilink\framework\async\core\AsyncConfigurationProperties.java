package com.tui.destilink.framework.async.core;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import static com.tui.destilink.framework.async.core.AsyncConfigurationProperties.PREFIX;

@Data
@Validated
@ConfigurationProperties(PREFIX)
public class AsyncConfigurationProperties {

    public static final String PREFIX = "destilink.fw.async.core";

    private int maxThreads = 10;
    private boolean enabled = true;
    private boolean fair = true;
}
