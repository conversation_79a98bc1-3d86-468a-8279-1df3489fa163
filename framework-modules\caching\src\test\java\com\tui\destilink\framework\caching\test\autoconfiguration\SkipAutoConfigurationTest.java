package com.tui.destilink.framework.caching.test.autoconfiguration;

import com.tui.destilink.framework.caching.cachemanager.CompositeCacheManager;
import com.tui.destilink.framework.caching.config.CachingProperties;
import com.tui.destilink.framework.caching.inject.InjectCachePostProcessor;
import com.tui.destilink.framework.caching.test.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.SchedulingConfigurer;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
class SkipAutoConfigurationTest {

    @Autowired(required = false)
    private CachingProperties cachingProps;

    @Autowired(required = false)
    private CompositeCacheManager cacheManager;

    @Qualifier("inMemoryCacheSchedulingConfigurer")
    @Autowired(required = false)
    private SchedulingConfigurer schedulingConfigurer;

    @Autowired(required = false)
    private InjectCachePostProcessor injectCachePostProcessor;

    @Test
    void testAllNull() {
        assertThat(cachingProps).isNull();
        assertThat(cacheManager).isNull();
        assertThat(schedulingConfigurer).isNull();
        assertThat(injectCachePostProcessor).isNull();
    }

}
