package com.tui.destilink.framework.cloudevents.aws.sns.publish;

import com.fasterxml.jackson.databind.JsonNode;
import com.tui.destilink.framework.cloudevents.aws.sns.publish.SnsCloudEventsPublisherContext.SnsCloudEventsPublisherContextBuilder;
import com.tui.destilink.framework.cloudevents.config.CloudEventsConfigProperties;
import com.tui.destilink.framework.cloudevents.context.attribute.dataschema.CloudEventsDataSchemaResolver;
import com.tui.destilink.framework.cloudevents.context.attribute.type.CloudEventsTypeResolver;
import com.tui.destilink.framework.cloudevents.publish.CloudEventsPublisherContextFactory;
import io.awspring.cloud.sns.core.TopicArnResolver;
import io.cloudevents.CloudEvent;
import jakarta.validation.Validator;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import software.amazon.awssdk.arns.Arn;

import java.net.URI;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SnsCloudEventsPublisherContextFactory extends CloudEventsPublisherContextFactory {

    private final TopicArnResolver topicArnResolver;
    private final SnsCloudEventsTargetResolver snsTargetResolver;

    private final Map<Map.Entry<Class<?>, Arn>, SnsCloudEventsPublisherContext<?>> cache = new ConcurrentHashMap<>();
    private final Map<Triple<String, URI, Arn>, SnsCloudEventsPublisherContext<JsonNode>> jsonNodeCache = new ConcurrentHashMap<>();

    public SnsCloudEventsPublisherContextFactory(TopicArnResolver topicArnResolver, SnsCloudEventsTargetResolver snsTargetResolver, CloudEventsTypeResolver typeResolver, CloudEventsDataSchemaResolver dataSchemaResolver, Validator validator, CloudEventsConfigProperties.PublisherProperties publisherProps) {
        super(validator, typeResolver, dataSchemaResolver, publisherProps);
        this.topicArnResolver = topicArnResolver;
        this.snsTargetResolver = snsTargetResolver;
    }

    public <T> SnsCloudEventsPublisherContextBuilder<T, ?, ?> /* NOSONAR */ builder(Class<T> payloadClass, String topicName) {
        return builder(payloadClass, topicArnResolver.resolveTopicArn(topicName));
    }

    @SuppressWarnings("unchecked")
    public <T> SnsCloudEventsPublisherContextBuilder<T, ?, ?> /* NOSONAR */ builder(Class<T> payloadClass, Arn topicArn) {
        Map.Entry<Class<?>, Arn> key = Map.entry(payloadClass, topicArn);
        return (SnsCloudEventsPublisherContextBuilder<T, ?, ?>) cache.computeIfAbsent(key, k -> buildContext(payloadClass, topicArn))
                // Always create a new object
                .toBuilder();
    }

    public SnsCloudEventsPublisherContextBuilder<JsonNode, ?, ?> /* NOSONAR */ builder(CloudEvent cloudEvent, String topicName) {
        return builder(cloudEvent, topicArnResolver.resolveTopicArn(topicName));
    }

    public SnsCloudEventsPublisherContextBuilder<JsonNode, ?, ?> /* NOSONAR */ builder(CloudEvent cloudEvent, Arn topicArn) {
        Triple<String, URI, Arn> key = ImmutableTriple.of(cloudEvent.getType(), cloudEvent.getDataSchema(), topicArn);
        return jsonNodeCache.computeIfAbsent(key, k -> buildContext(cloudEvent, topicArn))
                // Always create a new object
                .toBuilder();
    }

    private <T> SnsCloudEventsPublisherContext<T> buildContext(Class<T> payloadClass, Arn topicArn) {
        SnsCloudEventsPublisherContextBuilder<T, ?, ?> builder = SnsCloudEventsPublisherContext.builder();
        builder.payloadClazz(payloadClass)
                .resultClazz(SnsCloudEventsPublishResult.class)
                .type(resolveType(payloadClass))
                .dataSchema(resolveDataSchema(payloadClass))
                .source(t -> defaultSource())
                .dataValidator(defaultDataValidator())
                .dataRefTarget(buildDataRefTarget(topicArn));
        return builder.topicArn(topicArn).build();
    }

    private SnsCloudEventsPublisherContext<JsonNode> buildContext(CloudEvent cloudEvent, Arn topicArn) {
        SnsCloudEventsPublisherContextBuilder<JsonNode, ?, ?> builder = SnsCloudEventsPublisherContext.builder();
        builder.payloadClazz(JsonNode.class)
                .resultClazz(SnsCloudEventsPublishResult.class)
                .type(cloudEvent.getType())
                .dataSchema(cloudEvent.getDataSchema())
                .dataRefTarget(buildDataRefTarget(topicArn));
        return builder.topicArn(topicArn).build();
    }

    private SnsS3ApCloudEventsPublisherDataRefTarget buildDataRefTarget(Arn topicArn) {
        if (supportsDataRef()) {
            SnsCloudEventsTargetResolver.SnsTarget target = snsTargetResolver.resolve(topicArn);
            return SnsS3ApCloudEventsPublisherDataRefTarget.builder()
                    .baseUri(SnsS3ApCloudEventsPublisherDataRefTarget.buildBaseUri(target.getS3AccessPointAlias(), target.getS3Prefix()))
                    .scope(SnsS3ApCloudEventsPublisherDataRefTarget.S3ApDataRefScope.valueOf(target.getType().toString().toUpperCase(Locale.ROOT)))
                    .apArn(target.getArn())
                    .apId(target.getS3AccessPointId())
                    .apName(target.getName())
                    .apAlias(target.getS3AccessPointAlias())
                    .apDomainName(target.getS3AccessPointDomainName())
                    .apPrefix(target.getS3Prefix())
                    .build();
        }
        return SnsS3ApCloudEventsPublisherDataRefTarget.builder().build();
    }

}
