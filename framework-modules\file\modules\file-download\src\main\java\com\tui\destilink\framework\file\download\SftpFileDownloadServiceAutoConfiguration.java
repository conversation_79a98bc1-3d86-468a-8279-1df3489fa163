package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.download.impl.SftpFileDownloadServiceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;

@RequiredArgsConstructor
@AutoConfiguration
@ConditionalOnClass(DefaultSftpSessionFactory.class)
public class SftpFileDownloadServiceAutoConfiguration {

    @Bean
    AbstractFileDownloadServiceFactory sftpFileDownloadServiceFactory() {
        return new SftpFileDownloadServiceFactory();
    }
}
