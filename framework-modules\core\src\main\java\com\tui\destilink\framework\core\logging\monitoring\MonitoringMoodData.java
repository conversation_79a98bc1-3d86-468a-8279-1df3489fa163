package com.tui.destilink.framework.core.logging.monitoring;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Data
public class MonitoringMoodData implements MonitoringData {

    @Getter
    @RequiredArgsConstructor
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum Mood {
        MR_HANKEY("\uD83D\uDCA9"),
        GRINNING("\uD83D\uDE00"),
        ROFL("\uD83E\uDD23"),
        UPSIDE_DOWN("\uD83D\uDE43"),
        FLUSHED("\uD83D\uDE33"),
        SMIRKING("\uD83D\uDE0F"),
        SLEEPING("\uD83D\uDE34"),
        ZANY("\uD83E\uDD2A"),
        THINKING("\uD83E\uDD14"),
        ZIPPER_MOUTH("\uD83E\uDD10"),
        NAUSEATED("\uD83E\uDD22"),
        VOMITING("\uD83E\uDD2E"),
        WOOZY("\uD83E\uDD74"),
        CONFUSED("\uD83D\uDE15"),
        FEARFUL("\uD83D\uDE28"),
        POUTING("\uD83D\uDE21"),
        ANGRY("\uD83D\uDE20"),
        CENSORED_SYMBOLS_ON_MOUTH("\uD83E\uDD2C"),
        SKULL("\uD83D\uDC80"),
        CLOWN("\uD83E\uDD21"),
        BOMB("\uD83D\uDCA3"),
        SHUSHING("\uD83E\uDD2B");
        private final String value;

        @JsonValue
        public String getValue() {
            return value;
        }
    }

    private final Mood mood;
}
