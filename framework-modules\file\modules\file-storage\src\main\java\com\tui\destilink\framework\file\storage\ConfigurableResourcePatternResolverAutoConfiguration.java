package com.tui.destilink.framework.file.storage;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Optional;

@AutoConfiguration
public class ConfigurableResourcePatternResolverAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    ConfigurableResourcePatternResolver configurableResourcePatternResolver(ApplicationContext applicationContext,
                                                                            Optional<List<ConfigurableResourcePatternResolverCustomizer>> customizer) {

        ConfigurableResourcePatternResolver resolver = new ConfigurableResourcePatternResolver(applicationContext);

        if (customizer.isPresent()) {
            customizer.get().forEach(c -> c.customize(resolver));
        }

        return resolver;
    }
}
