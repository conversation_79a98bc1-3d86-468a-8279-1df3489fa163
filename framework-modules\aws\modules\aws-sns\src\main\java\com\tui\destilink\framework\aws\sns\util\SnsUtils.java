package com.tui.destilink.framework.aws.sns.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties;
import com.tui.destilink.framework.aws.sns.logger.SnsLogFormatter;
import com.tui.destilink.framework.aws.sns.logger.SnsLogWriter;
import com.tui.destilink.framework.aws.sns.send.DefaultSnsSender;
import com.tui.destilink.framework.aws.sns.send.SnsSender;
import com.tui.destilink.framework.aws.sns.send.fifo.FifoAttributesEnricher;
import io.awspring.cloud.sns.core.TopicArnResolver;
import lombok.experimental.UtilityClass;
import software.amazon.awssdk.services.sns.SnsClient;

@UtilityClass
public class SnsUtils {

    public static SnsSender getSnsSender(SnsClient snsClient,
                                         ObjectMapper objectMapper,
                                         TopicArnResolver topicArnResolver,
                                         DefaultSnsContainerProperties properties,
                                         FifoAttributesEnricher fifoAttributesEnricher) {
        final SnsLogWriter snsLogWriter = SnsUtils.getSnsLogWriter(properties.getSnsLogging(), objectMapper);
        return new DefaultSnsSender(snsClient, topicArnResolver, objectMapper, snsLogWriter, fifoAttributesEnricher, properties);
    }

    public static SnsLogWriter getSnsLogWriter(DefaultSnsContainerProperties.SnsLogging snsLogging, ObjectMapper objectMapper) {
        return new SnsLogWriter(snsLogging, getSnsLogFormatter(snsLogging, objectMapper));
    }

    public static SnsLogFormatter getSnsLogFormatter(DefaultSnsContainerProperties.SnsLogging snsLogging, ObjectMapper objectMapper) {
        return new SnsLogFormatter(snsLogging, objectMapper);
    }
}