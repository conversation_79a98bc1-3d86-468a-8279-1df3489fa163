<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.web</groupId>
        <artifactId>web</artifactId>
        <version>1.0.27</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>web-client</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework.web</groupId>
            <artifactId>web-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>context-propagation</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-spring-test-listener</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>