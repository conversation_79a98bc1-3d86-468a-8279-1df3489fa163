package com.tui.destilink.framework.core.metrics.statsd;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.HashSet;
import java.util.Set;

@Data
@Validated
@ConfigurationProperties("destilink.fw.core.metrics.statsd")
public class StatsdFilterProperties {

    @NotNull
    private Boolean enableFilter = true;

    /**
     * A set of metrics to export via statsd.
     * All other metrics are filtered out.
     * Can be set to null to disable filtering.
     */
    private Set<String> nonFilteredMetrics = new HashSet<>();

}
