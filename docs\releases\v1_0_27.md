# [v1.0.27](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.27?ref_type=tags)

### :exploding_head: Version Updates

- Spring Boot 3.3.7 -> 3.3.12
- Spring Cloud AWS IO 3.2.0 -> 3.2.1
- AWS Advanced JDBC Wrapper 2.5.4 -> 2.5.6
- Logback Logstash Encoder 8.0 -> 8.1
- Swagger-parser-v3 2.1.24 -> 2.1.29
- Keycloak Admin Client 26.0.3 -> 26.0.5
- Lombok 1.18.36 -> 1.18.38
- Mapstruct Extensions Spring 1.1.3 -> 1.1.3
- Context Propagation 1.1.2 -> 1.1.3
- Guava 33.4.0-jre -> 33.4.8-jre
- Swagger Core v3 2.2.28 -> 2.2.32
- Git Commit Id Maven Plugin 9.0.1 -> 9.0.2

### :fire: Improvements
- updated LoggingContextFilter: should not be used to update layer anymore.
  Instead use ContextAutoConfiguration for setting up the layer.
- docker-compose files updated