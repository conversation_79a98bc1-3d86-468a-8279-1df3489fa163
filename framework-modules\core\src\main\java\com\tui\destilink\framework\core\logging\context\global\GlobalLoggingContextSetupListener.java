package com.tui.destilink.framework.core.logging.context.global;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;

public class GlobalLoggingContextSetupListener implements ApplicationListener<ApplicationStartedEvent> {

    private final ObjectProvider<GlobalContextProvider> globalContextProviders;

    public GlobalLoggingContextSetupListener(ObjectProvider<GlobalContextProvider> globalContextProviders) {
        this.globalContextProviders = globalContextProviders;
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        globalContextProviders.orderedStream().forEach(globalContextProvider
                -> GlobalContextJsonProvider.GLOBAL_CONTEXT.putAll(globalContextProvider.provide()));
    }
}
