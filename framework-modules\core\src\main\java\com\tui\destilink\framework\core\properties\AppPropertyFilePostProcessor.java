package com.tui.destilink.framework.core.properties;

import com.tui.destilink.framework.core.app.AppProperties;
import com.tui.destilink.framework.core.app.AppProperties.AppType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.OriginTrackedMapPropertySource;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.util.Locale;
import java.util.stream.Stream;

@Slf4j
@Order(AppPropertyFilePostProcessor.ORDER)
public class AppPropertyFilePostProcessor extends AbstractFwPropertiesFilePostProcessor {

    /**
     * Must be executed AFTER the ConfigDataEnvironmentPostProcessor
     */
    public static final int ORDER = ConfigDataEnvironmentPostProcessor.ORDER + 1;

    @Override
    protected void doPostProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {
        AppType appType = getAppType(env);
        FwCompositePropertySource fwPropertySource = getFwCompositePropertySource(env);

        loadPropertySources(appType).forEachOrdered(fwPropertySource::addAppTypePropertySource);
    }

    private AppProperties.AppType getAppType(Environment env) {
        return Binder.get(env)
                .bindOrCreate(AppProperties.PREFIX, AppProperties.class)
                .getType();
    }

    private Stream<OriginTrackedMapPropertySource> loadPropertySources(AppType appType) {
        String[] prefixes = Arrays.copyOf(PROPERTY_FILE_PATH_PREFIXES, PROPERTY_FILE_PATH_PREFIXES.length);
        for (int i = 0; i < prefixes.length; i++) {
            prefixes[i] = prefixes[i] + "." + appType.name().toLowerCase(Locale.ROOT);
        }
        return loadPropertySourcesStream(prefixes, PROPERTY_FILE_PATH_SUFFIXES);
    }

}
