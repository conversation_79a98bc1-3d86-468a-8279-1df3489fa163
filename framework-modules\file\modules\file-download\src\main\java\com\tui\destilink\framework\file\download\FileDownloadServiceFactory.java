package com.tui.destilink.framework.file.download;

import com.tui.destilink.framework.file.storage.StorageService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.util.List;

@RequiredArgsConstructor
public class FileDownloadServiceFactory extends AbstractFactoryBean<FileDownloadService> {

    private final DownloadConfiguration configuration;
    private final StorageService storageService;
    private final ResourcePatternResolver resourcePatternResolver;
    private final List<AbstractFileDownloadServiceFactory> factories;

    @Override
    public Class<?> getObjectType() {
        return FileDownloadService.class;
    }

    @Override
    public boolean isSingleton() {
        return false;
    }

    @Override
    protected FileDownloadService createInstance() throws Exception {
        for (AbstractFileDownloadServiceFactory factory : this.factories) {
            if (factory.canHandle(this.configuration.getType())) {
                return factory.create(this.configuration, this.storageService, this.resourcePatternResolver);
            }
        }

        throw new IllegalArgumentException("No factory for FileDownloadService found with type " + this.configuration.getType());
    }

}
