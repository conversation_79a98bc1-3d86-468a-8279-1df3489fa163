package com.tui.destilink.framework.redis.core.util;

import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;

public class RedisKeyPrefix extends AbstractRedisKey<RedisKeyPrefix> {

    private static final RedisKeyPrefix EMPTY = new RedisKeyPrefix("");

    protected RedisKeyPrefix(String value) {
        super(value);
    }

    public String getPrefix() {
        String value = super.getValue();
        if (value.isEmpty()) {
            return value;
        }
        return value + RedisKeyUtils.KEY_DELIMITER;
    }

    public byte[] getPrefixBytes() {
        return CODEC.encodeKey(getPrefix()).array();
    }

    public RedisKey toRedisKey() {
        return new RedisKey(getValue());
    }

    public PatternTopic toPatternTopic() {
        return new PatternTopic(getPrefix() + "*");
    }

    public String buildRedisKey(String keySuffix) {
        return getPrefix() + keySuffix;
    }

    public String buildRedisKey(String... keySuffix) {
        return getPrefix() + RedisKeyUtils.join(keySuffix);
    }

    public String buildRedisKeyWithHashTag(String... keySuffix) {
        return getPrefix() + RedisKeyUtils.joinHashTag(keySuffix);
    }

    @Override
    public String toString() {
        return getPrefix();
    }

    @Override
    protected RedisKeyPrefix newObject(String value) {
        return new RedisKeyPrefix(value);
    }

    public static RedisKeyPrefix of() {
        return EMPTY;
    }

    public static RedisKeyPrefix of(String element) {
        if (StringUtils.hasText(element)) {
            return new RedisKeyPrefix(element);
        }
        return EMPTY;
    }

    public static RedisKeyPrefix of(String... elements) {
        return of(Arrays.asList(elements));
    }

    public static RedisKeyPrefix of(Collection<String> elements) {
        if (elements.isEmpty()) {
            return EMPTY;
        } else if (elements.size() == 1) {
            return of(elements.stream().findFirst().orElseThrow());
        }
        return new RedisKeyPrefix(RedisKeyUtils.join(elements));
    }

    @Override
    public int hashCode() {
        return getPrefix().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return (obj instanceof RedisKeyPrefix rkp) && getValue().equals(rkp.getValue());
    }
}
