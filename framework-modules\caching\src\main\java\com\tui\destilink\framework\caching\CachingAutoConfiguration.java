package com.tui.destilink.framework.caching;

import com.tui.destilink.framework.caching.config.CachingConfigCustomizer;
import com.tui.destilink.framework.caching.config.CachingConfigProvider;
import com.tui.destilink.framework.caching.config.CachingProperties;
import com.tui.destilink.framework.caching.inject.InjectCachePostProcessor;
import com.tui.destilink.framework.core.condition.ConditionalOnPropertyPrefix;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@AutoConfiguration
public class CachingAutoConfiguration {

    @Configuration(proxyBeanMethods = false)
    @EnableConfigurationProperties(CachingProperties.class)
    @ConditionalOnPropertyPrefix(CachingProperties.PROPERTIES_PREFIX + ".caches")
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    static class ConfigureWithCachingProperties {
        // Helper to enable properties only if caches are configured under ".caches"
    }

    @Configuration(proxyBeanMethods = false)
    @EnableConfigurationProperties(CachingProperties.class)
    @ConditionalOnBean(CachingConfigCustomizer.class)
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    static class ConfigureWithCustomizer {
        // Helper to enable properties only if CachingConfigCustomizer beans are available
    }

    @Bean
    @ConditionalOnBean(CachingProperties.class)
    @ConditionalOnMissingBean
    public CachingConfigProvider cachingConfigProvider(CachingProperties cachingProps, ObjectProvider<CachingConfigCustomizer> customizers) {
        CachingConfigProvider provider = new CachingConfigProvider(cachingProps);
        customizers.orderedStream().forEach(c -> c.customize(provider));
        return provider;
    }

    /**
     * Bean is defined in static method to make sure the postprocessor
     * is instantiated as early as possible
     *
     * @return The InjectCachePostProcessor bean
     */
    @Bean
    @ConditionalOnBean(CachingConfigProvider.class)
    public static InjectCachePostProcessor injectCachePostProcessor() {
        return new InjectCachePostProcessor();
    }

}
