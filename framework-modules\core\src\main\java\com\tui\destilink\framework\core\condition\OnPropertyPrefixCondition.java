package com.tui.destilink.framework.core.condition;

import org.springframework.boot.autoconfigure.condition.ConditionMessage;
import org.springframework.boot.autoconfigure.condition.ConditionOutcome;
import org.springframework.boot.autoconfigure.condition.SpringBootCondition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.annotation.MergedAnnotation;
import org.springframework.core.annotation.MergedAnnotationPredicates;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.Assert;

import java.util.*;

public class OnPropertyPrefixCondition extends SpringBootCondition {

    @Override
    @SuppressWarnings({"rawtypes"})
    public ConditionOutcome getMatchOutcome(ConditionContext context, AnnotatedTypeMetadata metadata) {
        List<AnnotationAttributes> allAnnotationAttributes = metadata.getAnnotations()
                .stream(ConditionalOnPropertyPrefix.class.getName())
                .filter(MergedAnnotationPredicates.unique(MergedAnnotation::getMetaTypes))
                .map(MergedAnnotation::asAnnotationAttributes)
                .toList();

        List<EnumerablePropertySource> propertySources = getEnumerablePropertySources(context);

        List<ConditionMessage> noMatch = new ArrayList<>();
        List<ConditionMessage> match = new ArrayList<>();

        for (AnnotationAttributes annotationAttributes : allAnnotationAttributes) {
            ConditionOutcome outcome = determineOutcome(annotationAttributes, propertySources);
            (outcome.isMatch() ? match : noMatch).add(outcome.getConditionMessage());
        }

        if (!noMatch.isEmpty()) {
            return ConditionOutcome.noMatch(ConditionMessage.of(noMatch));
        }
        return ConditionOutcome.match(ConditionMessage.of(match));
    }

    @SuppressWarnings({"rawtypes"})
    private ConditionOutcome determineOutcome(AnnotationAttributes annotationAttributes, List<EnumerablePropertySource> propertySources) {
        final String prefix = getPrefix(annotationAttributes);
        final Optional<String> result = propertySources.stream()
                .flatMap(ps -> Arrays.stream(ps.getPropertyNames()))
                .filter(pn -> pn.startsWith(prefix))
                .findAny();

        if (result.isPresent()) {
            return ConditionOutcome.match(
                    ConditionMessage
                            .forCondition(ConditionalOnPropertyPrefix.class, result.get())
                            .because("matched"));
        }
        return ConditionOutcome.noMatch(
                ConditionMessage
                        .forCondition(ConditionalOnPropertyPrefix.class)
                        .didNotFind("prefix")
                        .items(ConditionMessage.Style.QUOTE, prefix));
    }

    @SuppressWarnings({"rawtypes"})
    private List<EnumerablePropertySource> getEnumerablePropertySources(ConditionContext context) {
        return ((ConfigurableEnvironment) context.getEnvironment())
                .getPropertySources().stream()
                .filter(EnumerablePropertySource.class::isInstance)
                .map(EnumerablePropertySource.class::cast)
                .toList();
    }

    private String getPrefix(Map<String, Object> annotationAttributes) {
        String prefix = ((String) annotationAttributes.getOrDefault("prefix", "")).trim();
        String value = ((String) annotationAttributes.getOrDefault("value", "")).trim();

        Assert.state(!value.isEmpty() || !prefix.isEmpty(),
                "The prefix or value attribute of @ConditionalOnPropertyPrefix must be specified");
        return value.isEmpty() ? prefix : value;
    }

}
