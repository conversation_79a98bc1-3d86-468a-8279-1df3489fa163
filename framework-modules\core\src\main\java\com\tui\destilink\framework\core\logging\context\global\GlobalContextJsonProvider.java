package com.tui.destilink.framework.core.logging.context.global;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fasterxml.jackson.core.JsonGenerator;
import net.logstash.logback.composite.AbstractFieldJsonProvider;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GlobalContextJsonProvider extends AbstractFieldJsonProvider<ILoggingEvent> {

    protected static final Map<String, String> GLOBAL_CONTEXT = new ConcurrentHashMap<>();

    @Override
    public void writeTo(JsonGenerator generator, ILoggingEvent iLoggingEvent) throws IOException {
        if (!GLOBAL_CONTEXT.isEmpty()) {
            for (Map.Entry<String, String> entry : GLOBAL_CONTEXT.entrySet()) {
                if (MDC.get(entry.getKey()) == null) {
                    generator.writeStringField(entry.getKey(), entry.getValue());
                }
            }
        }
    }
}
