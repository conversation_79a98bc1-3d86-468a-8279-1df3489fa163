package com.tui.destilink.framework.core.logging.customize.jsonprovider;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fasterxml.jackson.core.JsonGenerator;
import com.google.auto.service.AutoService;
import com.tui.destilink.framework.core.logging.customize.LogbackCustomizer;
import com.tui.destilink.framework.core.logging.marker.ConstraintViolationMarker;
import com.tui.destilink.framework.core.logging.util.IThrowableProxyUtils;
import jakarta.validation.ConstraintViolationException;
import net.logstash.logback.composite.AbstractJsonProvider;
import net.logstash.logback.encoder.LogstashEncoder;

import java.io.IOException;

@SuppressWarnings("unused")
@AutoService(LogbackCustomizer.class)
public class ConstraintViolationExceptionJsonProvider extends AbstractJsonProvider<ILoggingEvent> implements LogbackCustomizer {

    private static final String FIELD_NAME = "constraintViolations";

    @Override
    public void writeTo(JsonGenerator generator, ILoggingEvent iLoggingEvent) throws IOException {
        ConstraintViolationException exception = IThrowableProxyUtils
                .findNestedException(iLoggingEvent.getThrowableProxy(), ConstraintViolationException.class);
        if (exception != null) {
            ConstraintViolationMarker marker = ConstraintViolationMarker.of(exception);
            generator.writeObjectField(FIELD_NAME, marker.getFieldValue());
        }
    }

    @Override
    public void customize(LogstashEncoder encoder) {
        encoder.addProvider(this);
    }
}
