package com.tui.destilink.framework.core.util;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;
import lombok.experimental.UtilityClass;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;

import java.util.Set;

@UtilityClass
public class ConfigurationPropertyUtils {

    public static <T> T loadAndValidate(Environment env, String propertiesPrefix, Class<T> target) {
        try (ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            final T properties = load(env, propertiesPrefix, target);
            Set<ConstraintViolation<T>> violations = validatorFactory.getValidator().validate(properties);
            if (!violations.isEmpty()) {
                throw new ConstraintViolationException(violations);
            }
            return properties;
        }
    }

    public static <T> T load(Environment env, String propertiesPrefix, Class<T> target) {
        return Binder.get(env).bindOrCreate(propertiesPrefix, target);
    }

}
