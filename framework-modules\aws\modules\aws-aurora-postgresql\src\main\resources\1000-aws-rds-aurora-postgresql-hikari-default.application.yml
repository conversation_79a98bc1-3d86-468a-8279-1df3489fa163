destilink:
  fw:
    aws:
      rds:
        aurora:
          postgresql:
            hikari:
              iam-auth:
                enabled: false
                username-prefix: "iam-"
              default-properties:
                wrapperPlugins: "driverMetaData,auroraConnectionTracker,failover2,efm2"
                wrapperDriverName: "PostgreSQL JDBC Driver"
                monitoring-connectTimeout: 10
                monitoring-socketTimeout: 10
                failoverClusterTopologyRefreshRateMs: 2000
                failoverWriterReconnectIntervalMs: 2000
                reWriteBatchedInserts: true
                ssl: true
                sslmode: require
                tcpKeepAlive: true
                ApplicationName: ${DD_SERVICE:hey-sexy-service}
              exception-override-class-name: software.amazon.jdbc.util.HikariCPSQLException
              read-only-hikari-max-lifetime-ms: 300000 # 5min