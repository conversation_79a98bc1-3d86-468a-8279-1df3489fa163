package com.tui.destilink.framework.cloudevents.context.extension.tui.pd;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class PdPersonRef {

    @NotBlank
    private final String id;
    @NotNull
    private final PdPersonRefSystem system;
    @NotNull
    private final PdPersonRefTenant tenant;

    @AssertTrue(message = "system and tenant must not be UNKNOWN")
    protected boolean isValid() {
        return !PdPersonRefSystem.UNKNOWN.equals(system) && !PdPersonRefTenant.UNKNOWN.equals(tenant);
    }
}
