package com.tui.destilink.framework.cloudevents.exception;

import io.cloudevents.CloudEvent;

public class CloudEventsPublishException extends CloudEventsException {
    public CloudEventsPublishException(String msg) {
        super(msg);
    }

    public CloudEventsPublishException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public CloudEventsPublishException(CloudEvent cloudevent, String msg) {
        super(cloudevent, msg);
    }

    public CloudEventsPublishException(CloudEvent cloudevent, String msg, Throwable cause) {
        super(cloudevent, msg, cause);
    }
}
