destilink:
  fw:
    aws:
      sqs:
        logging:
          enabled: true
        container:
          default:
            max-concurrent-messages: 100
            listener-shutdown-timeout: PT60S
            acknowledgement-shutdown-timeout: PT60S
            max-retries-no-dlq: 2
spring:
  cloud:
    aws:
      sqs:
        enabled: true
        queue-not-found-strategy: fail
        listener:
          max-concurrent-messages: ${destilink.fw.aws.sqs.container.default.max-concurrent-messages}