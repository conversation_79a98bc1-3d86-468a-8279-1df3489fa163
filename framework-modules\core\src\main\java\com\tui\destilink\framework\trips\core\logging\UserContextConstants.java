package com.tui.destilink.framework.trips.core.logging;

import lombok.experimental.UtilityClass;

// Datadog logging attributes for user info
@UtilityClass
public class UserContextConstants {

    public static final String USER_REPFIX = "usr"; // Allows you to associate logs to specific API clients
    private static final String SEPARATOR = ".";
    private static final String NAME = "name";
    // recommended
    public static final String USER_NAME = USER_REPFIX + SEPARATOR + NAME;
    private static final String ID = "id";
    public static final String USER_ID = USER_REPFIX + SEPARATOR + ID;
}
