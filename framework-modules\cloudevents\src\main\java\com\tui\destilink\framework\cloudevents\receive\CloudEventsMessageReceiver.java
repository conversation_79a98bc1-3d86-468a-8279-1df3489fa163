package com.tui.destilink.framework.cloudevents.receive;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.cloudevents.context.attribute.dataschema.CloudEventsDataSchemaResolver;
import com.tui.destilink.framework.cloudevents.context.attribute.type.CloudEventsTypeResolver;
import com.tui.destilink.framework.cloudevents.context.extension.dataref.receive.CloudEventsDataRefReceiver;
import com.tui.destilink.framework.cloudevents.context.extension.tui.business.TuiBusinessExtension;
import com.tui.destilink.framework.cloudevents.context.extension.tui.map.GenericMapExtension;
import com.tui.destilink.framework.cloudevents.context.extension.tui.pd.TuiPdExtension;
import com.tui.destilink.framework.cloudevents.exception.CloudEventsDataMappingException;
import com.tui.destilink.framework.cloudevents.exception.CloudEventsMappingException;
import com.tui.destilink.framework.cloudevents.receive.messaging.CloudEventsMessageUtils;
import com.tui.destilink.framework.core.messaging.message.util.IdTsAwareMessageUtils;
import io.awspring.cloud.sqs.MessageHeaderUtils;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventExtension;
import io.cloudevents.core.extensions.DatarefExtension;
import io.cloudevents.core.provider.ExtensionProvider;
import io.cloudevents.jackson.JsonCloudEventData;
import io.cloudevents.jackson.JsonFormat;
import io.cloudevents.spring.messaging.CloudEventMessageConverter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.util.MimeType;

import java.util.HashMap;
import java.util.Map;

import static com.tui.destilink.framework.cloudevents.receive.messaging.CloudEventsMessageUtils.HEADERS_EXT_DATAREF;

public class CloudEventsMessageReceiver extends CloudEventsReceiver<Message<?>, Message<?>> {

    public static final String DEFAULT_CONTENT_TYPE = JsonFormat.CONTENT_TYPE;

    private final CloudEventMessageConverter cloudEventMessageConverter;
    private final ObjectMapper objectMapper;

    public CloudEventsMessageReceiver(CloudEventsTypeResolver typeResolver, CloudEventsDataSchemaResolver dataSchemaResolver, CloudEventsDataRefReceiver dataRefReceiver, ObjectMapper objectMapper) {
        super(typeResolver, dataSchemaResolver, dataRefReceiver);
        this.cloudEventMessageConverter = new CloudEventMessageConverter();
        this.objectMapper = objectMapper;
    }

    @Override
    protected <P> CloudEvent toCloudEvent(Message<?> source, Class<P> targetClass) {
        try {
            return (CloudEvent) cloudEventMessageConverter.fromMessage(enrichCloudEventsContentTypeHeader(source), CloudEvent.class);
        } catch (Exception ex) {
            throw new CloudEventsMappingException("Failed to map source to CloudEvent", ex);
        }
    }

    @Override
    protected <P> ToPojo<P> getDataMapper(String dataContentType) {
        return (data, targetClass) -> {
            try {
                if (data instanceof JsonCloudEventData j) {
                    return objectMapper.treeToValue(j.getNode(), targetClass);
                }
                return objectMapper.readValue(data.toBytes(), targetClass);
            } catch (Exception ex) {
                throw new CloudEventsDataMappingException("Cannot map cloudevents data " + data.getClass() + " to pojo " + targetClass, ex);
            }
        };
    }

    @Override
    protected <P> Message<?> buildResult(Message<?> message, CloudEvent cloudEvent, String dataContentType, P data, Class<P> targetClass) {
        try {
            MessageHeaders headers = enrichCloudEventHeaders(message.getHeaders(), cloudEvent, dataContentType);
            return new GenericMessage<>(data, headers);
        } catch (Exception ex) {
            throw new CloudEventsMappingException("Failed to build result", ex);
        }
    }

    private Message<?> enrichCloudEventsContentTypeHeader(Message<?> source) {
        if (source.getHeaders().containsKey(MessageHeaders.CONTENT_TYPE)) {
            return source;
        }
        return MessageHeaderUtils.addHeaderIfAbsent(source, MessageHeaders.CONTENT_TYPE, DEFAULT_CONTENT_TYPE);
    }

    private MessageHeaders enrichCloudEventHeaders(MessageHeaders source, CloudEvent cloudEvent, String dataContentType) {
        Map<String, Object> newHeaders = HashMap.newHashMap(5);
        newHeaders.put(MessageHeaders.CONTENT_TYPE, MimeType.valueOf(dataContentType));
        newHeaders.put(CloudEventsMessageUtils.HEADERS_CLOUDEVENT, cloudEvent);
        newHeaders.put(CloudEventsMessageUtils.HEADERS_EXT_TUI_BUSINESS, extension(cloudEvent, TuiBusinessExtension.class));
        newHeaders.put(CloudEventsMessageUtils.HEADERS_EXT_TUI_PD, extension(cloudEvent, TuiPdExtension.class));
        newHeaders.put(CloudEventsMessageUtils.HEADERS_EXT_TUI_GENERIC_MAP, extension(cloudEvent, GenericMapExtension.class));
        if (cloudEvent.getExtension(DatarefExtension.DATAREF) != null) {
            newHeaders.put(HEADERS_EXT_DATAREF, extension(cloudEvent, DatarefExtension.class));
        }
        return IdTsAwareMessageUtils.addHeaders(source, newHeaders);
    }

    private <T extends CloudEventExtension> T extension(CloudEvent cloudEvent, Class<T> clazz) {
        return ExtensionProvider.getInstance().parseExtension(clazz, cloudEvent);
    }
}
