openapi: 3.0.3
info:
  title: BookingDataImportEvent
  version: 1.0.8

paths:
  /reservations:
    get:
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingDataImportEvent'

components:
  schemas:
    BookingDataImportEvent:
      type: object
      properties:
        Header:
          $ref: '#/components/schemas/Header'
        reservations:
          type: array
          items:
            $ref: '#/components/schemas/Reservation'

    Header:
      type: object
      properties:
        eventId:
          type: string
        businessProcessId:
          type: string
        businessProcessName:
          type: string
        eventType:
          type: string
        eventVersion:
          type: string
        producedTimeStamp:
          type: string
        producedBy:
          type: string
        producedByVersion:
          type: string

    Reservation:
      type: object
      properties:
        generalDetails:
          $ref: '#/components/schemas/GeneralDetails'
        bookingDetails:
          $ref: '#/components/schemas/BookingDetails'
        tourOperatorDetails:
          $ref: '#/components/schemas/TourOperatorDetails'
        supplierDetails:
          $ref: '#/components/schemas/SupplierDetails'
        technicalDetails:
          $ref: '#/components/schemas/TechnicalDetails'
        distributorDetails:
          $ref: '#/components/schemas/DistributorDetails'
        flightDetails:
          type: array
          items:
            $ref: '#/components/schemas/FlightDetail'
        paxDetails:
          type: array
          items:
            $ref: '#/components/schemas/PaxDetail'
        supplierPriceElements:
          $ref: '#/components/schemas/SupplierPriceElements'
        paymentDetails:
          $ref: '#/components/schemas/PaymentDetails'

    GeneralDetails:
      type: object
      properties:
        tuiSalesBookingId:
          type: string
        initialSourceSystem:
          type: string
        sourceSystem:
          type: string
        bookingStatus:
          type: string
        confirmationStatus:
          type: string
        bookingTimestamp:
          type: string
          format: date-time
        modificationTimestamp:
          type: string
          format: date-time
        cancellationTimestamp:
          type: string
          format: date-time
        dubhId:
          type: string
        dubhIdInternal:
          type: string
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
        traceCode:
          type: string
        region:
          type: string
        distributor:
          type: string
        legalEntity:
          type: string
        groupContextId:
          type: string

    BookingDetails:
      type: object
      properties:
        tuiSalesSuperRef:
          type: string
        tuiSalesBookingVersion:
          type: string
        optionExpiryDate:
          type: string
        nights:
          type: integer
          format: int32
        paxReference:
          type: array
          items:
            type: string
        numberPersons:
          type: integer
          format: int32
        numberAdults:
          type: integer
          format: int32
        numberChildren:
          type: integer
          format: int32
        numberInfants:
          type: integer
          format: int32
        packageStatus:
          type: string
        promotionCode:
          type: string
        promotionGroupCode:
          type: string
        sellingPromotionName:
          type: string
        sourceMarket:
          type: string
        costPromotion:
          type: string
        unitId:
          type: string
        unitVersion:
          type: string
        brand:
          type: string
        chainCode:
          type: string
        chainName:
          type: string
        tuiToSupplierMessages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        customerToSupplierMessages:
          type: array
          items:
            $ref: '#/components/schemas/Message'

    TourOperatorDetails:
      type: object
      properties:
        accoContractCode:
          type: string
        accoContractName:
          type: string
        roomContractCode:
          type: string
        roomContractName:
          type: string
        roomContractSupplierCode:
          type: string
        roomContractSupplierName:
          type: string
        costTimestamp:
          type: string
          format: date
        calculationTimestamp:
          type: string
          format: date
        boardCode:
          type: string
        tuiSalesGroupBookingId:
          type: string

    SupplierDetails:
      type: object
      properties:
        externalSupplierCode:
          type: string
        externalSubSupplierCode:
          type: string
        supplierSuperRef:
          type: string
        supplierBookingRef:
          type: string
        supplierTuiRef:
          type: string
        supplierCancelRef:
          type: string
        supplierRoomKey:
          type: string
        supplierRoomAdditionalKey:
          type: string
        supplierRoomName:
          type: string
        supplierRoomSubKey:
          type: string
        supplierRoomSubName:
          type: string
        supplierBoardTypeKey:
          type: string
        supplierBoardTypeName:
          type: string
        supplierBoardTypeSubKey:
          type: string
        supplierBoardTypeSubName:
          type: string
        supplierBoardTypeAdditionalKey:
          type: string
        externalSupplierId:
          type: string

    TechnicalDetails:
      type: object
      properties:
        dodAccommodationId:
          type: string
        dodRoomTypeId:
          type: string
        dodRoomAdditionalId:
          type: string
        dodBoardTypeId:
          type: string
        dodBoardTypeAdditionalId:
          type: string
        tripsProductReservationId:
          type: string
        tripsReservationComponentId:
          type: string
        tripsReservationUnitId:
          type: string
        atcoreAccoServiceSeqId:
          type: string
        isEstimateContract:
          type: boolean
        isCrossUnitBooking:
          type: boolean
        crossUnitBookingId:
          type: string
        isDynamic:
          type: boolean
        dbhInternalKey:
          type: string

    DistributorDetails:
      type: object
      properties:
        salesAgentType:
          type: string
        salesAgentCode:
          type: string
        salesAgentName:
          type: string
        salesAgentStreet:
          type: string
        salesAgentPostcode:
          type: string
        salesAgentCity:
          type: string
        salesAgentCountry:
          type: string
        salesAgentEmail:
          type: string
        salesAgentFax:
          type: string
        salesAgentMobile:
          type: string
        salesAgentPhone:
          type: string
        salesAgentId:
          type: string
        companyTraceCode:
          type: string
        companyName:
          type: string
        companyStreet:
          type: string
        companyPostcode:
          type: string
        companyCity:
          type: string
        companyCountry:
          type: string

    FlightDetail:
      type: object
      properties:
        bookingStatus:
          type: string
        confirmationStatus:
          type: string
        carrierCode:
          type: string
        flightNumber:
          type: string
        startDate:
          type: string
          format: date
        startTime:
          type: string
        startLocation:
          type: string
        endDate:
          type: string
          format: date
        endTime:
          type: string
        endLocation:
          type: string
        paxReference:
          type: array
          items:
            type: string
        flightServiceSeqId:
          type: string
        flightPnr:
          type: string

    PaxDetail:
      type: object
      properties:
        paxId:
          type: string
        seqId:
          type: integer
          format: int32
        paxConfirmationStatus:
          type: string
        gender:
          type: string
        ageType:
          type: string
        age:
          type: integer
          format: int32
        birth:
          type: string
          format: date
        givenName:
          type: string
        familyName:
          type: string
        nameTitle:
          type: string
        street:
          type: string
        postCode:
          type: string
        city:
          type: string
        country:
          type: string
        language:
          type: string
        phoneNumber:
          type: string
        mobileNumber:
          type: string
        email:
          type: string
        priceBeforeTaxAmount:
          type: string
        priceBeforeTaxCurrency:
          type: string
        purchaseRateAmount:
          type: string
        purchaseRateCurrency:
          type: string
        soldToPax:
          type: boolean
        paxStatus:
          type: string
        priceBeforeTaxPerPaxAmount:
          type: string
        priceBeforeTaxPerPaxCurrency:
          type: string
        purchaseRatePerPaxAmount:
          type: string
        purchaseRatePerPaxCurrency:
          type: string

    Message:
      type: object
      properties:
        category:
          type: string
        forwarded:
          type: boolean
        id:
          type: string
        text:
          type: string
        status:
          type: string

    SupplierPriceElements:
      type: object
      properties:
        priceBeforeTax:
          $ref: '#/components/schemas/PriceDetails'
        purchaseRate:
          $ref: '#/components/schemas/PriceDetails'
        salesOfferDetails:
          type: array
          items:
            $ref: '#/components/schemas/SalesOfferDetail'
        fee:
          type: object
          properties:
            modificationFee:
              $ref: '#/components/schemas/PriceDetails'
            cancellationFee:
              $ref: '#/components/schemas/PriceDetails'

    PaymentDetails:
      type: array
      items:
        type: object
        properties:
          virtualCreditCardFlag:
            type: string
          internalCardKey:
            type: string
          cardType:
            type: string
          type:
            type: string
          cardSupplierIdCode:
            type: string
          cardSubSupplierIdCode:
            type: string
          creationDateTime:
            type: string
          virtualCreditcardSupplierMainRef:
            type: string
          virtualCreditcardSupplierSubRef:
            type: string
          receivedCardRateAmount:
            type: number
          requestedCardRateCurrency:
            type: string
          currencyName:
            type: string
          cardStatus:
            type: string
          effectiveMinDate:
            type: string
            format: date
          effectiveMaxDate:
            type: string
            format: date

    PriceDetails:
      type: object
      properties:
        amount:
          type: number
        currency:
          type: string
        inDetail:
          $ref: '#/components/schemas/InDetail'

    InDetail:
      type: object
      properties:
        perDate:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              amount:
                type: number
              detailedPriceInformation:
                $ref: '#/components/schemas/DetailedPriceInformation'

    DetailedPriceInformation:
      type: object
      properties:
        inDetail:
          type: object
          properties:
            salesOfferIds:
              $ref: '#/components/schemas/SalesOfferIds'

    SalesOfferIds:
      type: array
      items:
        type: object
        properties:
          id:
            type: string
          amount:
            type: number

    SalesOfferDetail:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        offerType:
          type: string
        distributorCodes:
          type: array
          items:
            type: string
        onSaleOffer:
          type: boolean