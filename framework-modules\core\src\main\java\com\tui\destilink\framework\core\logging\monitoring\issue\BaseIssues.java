package com.tui.destilink.framework.core.logging.monitoring.issue;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum BaseIssues implements Issue {

    INTERNAL_SNS("InternalSNS"),
    EXTERNAL_SNS("ExternalSNS"),
    INTERNAL_HTTP("InternalHttpCommunication"),
    EXTERNAL_HTTP("ExternalHttpCommunication"),
    INTERNAL_AUTH("InternalAuthentication"),
    EXTERNAL_AUTH("ExternalAuthentication");

    private final String value;

    @JsonValue
    public String getValue() {
        return value;
    }
}
