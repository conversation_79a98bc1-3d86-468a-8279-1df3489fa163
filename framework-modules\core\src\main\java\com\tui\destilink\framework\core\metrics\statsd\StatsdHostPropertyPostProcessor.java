package com.tui.destilink.framework.core.metrics.statsd;

import com.tui.destilink.framework.core.properties.AbstractFwPostProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.export.statsd.StatsdProperties;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.util.Properties;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
@Order(StatsdHostPropertyPostProcessor.ORDER)
public class StatsdHostPropertyPostProcessor extends AbstractFwPostProcessor {

    /**
     * Must be executed at latest as possible
     */
    public static final int ORDER = Ordered.LOWEST_PRECEDENCE;

    public static final String STATSD_PROPERTIES_PREFIX = "management.statsd.metrics.export";

    public static final String STATSD_HOST_PROPERTY = "management.statsd.metrics.export.host";

    public static final String UNIX_DOMAIN_SOCKET_PREFIX = "unix:";

    @Override
    protected void doPostProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {
        StatsdProperties statsdProperties = loadProperties(env);
        if (StringUtils.startsWithIgnoreCase(statsdProperties.getHost(), UNIX_DOMAIN_SOCKET_PREFIX)) {
            log.debug("Found statsd uds host with prefix {}", keyValue(STATSD_HOST_PROPERTY, statsdProperties.getHost()));
            try {
                String strippedHost = new URI(statsdProperties.getHost()).getPath();
                env.getPropertySources().addFirst(buildPropertySource(strippedHost));
                log.debug("Added property source with stripped statsd host property {}", keyValue(STATSD_HOST_PROPERTY, strippedHost));
            } catch (Exception ex) {
                throw new IllegalArgumentException("Failed to strip " + UNIX_DOMAIN_SOCKET_PREFIX + " from host property " + statsdProperties.getHost());
            }
        }
    }

    private StatsdProperties loadProperties(ConfigurableEnvironment env) {
        try {
            return Binder.get(env).bind(STATSD_PROPERTIES_PREFIX, StatsdProperties.class).get();
        } catch (Exception ex) {
            throw new IllegalArgumentException("Failed to load StatsdProperties with prefix " + STATSD_PROPERTIES_PREFIX);
        }
    }

    private PropertiesPropertySource buildPropertySource(String host) {
        final Properties properties = new Properties();
        properties.setProperty(STATSD_HOST_PROPERTY, host);
        return new PropertiesPropertySource(buildFwOverridePropSourceName("StatsDHostPropertyCustomizer"), properties);
    }

}
