package com.tui.destilink.framework.cloudevents.it;

import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryable;
import com.tui.destilink.framework.cloudevents.receive.messaging.CloudEventsMessageUtils;
import com.tui.destilink.framework.core.logging.context.TripsContextDecorator;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import io.cloudevents.CloudEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.tui.destilink.framework.cloudevents.it.AbstractEnd2EndIT.CE_FIFO_QUEUE_NAME;
import static com.tui.destilink.framework.cloudevents.it.AbstractEnd2EndIT.CE_QUEUE_NAME;

@Slf4j
@Component
class SqsConsumers {

    protected final AtomicInteger legacyMessageCounter = new AtomicInteger(0);
    protected final AtomicInteger legacyFifoMessageCounter = new AtomicInteger(0);
    protected final AtomicInteger ceMessageCounter = new AtomicInteger(0);
    protected final AtomicInteger ceFifoMessageCounter = new AtomicInteger(0);

    protected final AtomicReference<UUID> ceMessageBusinessProcessId = new AtomicReference<>();
    protected final AtomicReference<UUID> ceFifoMessageBusinessProcessId = new AtomicReference<>();

    protected final Map<String, Message<?>> rxCeMessages = new ConcurrentHashMap<>();

    @SqsListener(acknowledgementMode = "ON_SUCCESS", queueNames = {"${test-support.localstack.messaging." + CE_QUEUE_NAME + ".name}"}, messageVisibilitySeconds = "2")
    public void listen(Message<?> message, SqsListenerRetryable sqsListenerRetryable, Acknowledgement acknowledgement) {
        CloudEvent cloudEvent = CloudEventsMessageUtils.getOptionalCloudEvent(message).orElse(null);
        if (cloudEvent != null) {
            rxCeMessages.put(cloudEvent.getId(), message);
            ceMessageBusinessProcessId.set(TripsContextDecorator.getInstance().getBusinessProcessId().get());
            ceMessageCounter.incrementAndGet();
        } else {
            legacyMessageCounter.incrementAndGet();
        }
        acknowledgement.acknowledge();
    }

    @SqsListener(acknowledgementMode = "ON_SUCCESS", queueNames = {"${test-support.localstack.messaging." + CE_FIFO_QUEUE_NAME + ".name}"}, messageVisibilitySeconds = "120")
    public void listenFifo(Message<?> message, SqsListenerRetryable sqsListenerRetryable, Acknowledgement acknowledgement) {
        CloudEvent cloudEvent = CloudEventsMessageUtils.getOptionalCloudEvent(message).orElse(null);
        if (cloudEvent != null) {
            rxCeMessages.put(cloudEvent.getId(), message);
            ceFifoMessageBusinessProcessId.set(TripsContextDecorator.getInstance().getBusinessProcessId().get());
            ceFifoMessageCounter.incrementAndGet();
        } else {
            legacyFifoMessageCounter.incrementAndGet();
        }
        acknowledgement.acknowledge();
    }
}
