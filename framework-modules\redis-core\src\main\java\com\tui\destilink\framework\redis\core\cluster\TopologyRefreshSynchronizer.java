package com.tui.destilink.framework.redis.core.cluster;

import com.tui.destilink.framework.core.util.FutureUtils;
import io.lettuce.core.RedisCommandExecutionException;
import io.lettuce.core.RedisCommandTimeoutException;
import io.lettuce.core.cluster.RedisClusterClient;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class TopologyRefreshSynchronizer {

    private static final Long REFRESH_MAX_AGE_MS = 1000L;

    private final RedisClusterClient redisClusterClient;

    private final ReentrantLock lock = new ReentrantLock();

    private volatile long lastUpdateTimestamp = 0;
    private CompletableFuture<Void> promise;

    public TopologyRefreshSynchronizer(RedisClusterClient redisClusterClient) {
        this.redisClusterClient = redisClusterClient;
    }

    public CompletableFuture<Void> refreshTopologyIfNecessary(Throwable throwable) {
        if (throwable instanceof RedisCommandExecutionException ex
                && (ex.getMessage().startsWith("MOVED") || ex.getMessage().startsWith("CLUSTERDOWN"))) {
            return refreshTopology();
        }
        if (throwable instanceof RedisCommandTimeoutException) {
            return refreshTopology();
        }
        return FutureUtils.COMPLETED_FUTURE;
    }

    public CompletableFuture<Void> refreshTopology() {
        lock.lock();
        try {
            if (promise == null
            || (System.currentTimeMillis() - lastUpdateTimestamp > REFRESH_MAX_AGE_MS)
            || promise.isCompletedExceptionally()) {
                promise = refresh();
            }
            return promise;
        } finally {
            lock.unlock();
        }
    }

    public long timeSinceLastTopologyRefreshMs() {
        return System.currentTimeMillis() - lastUpdateTimestamp;
    }

    private CompletableFuture<Void> refresh() {
        lastUpdateTimestamp = System.currentTimeMillis();
        return redisClusterClient.refreshPartitionsAsync()
                .thenAccept(v -> redisClusterClient.getPartitions().updateCache()) // TODO check if update is required -> expensive
                .whenComplete((v, t) -> {
                    if (t != null) {
                        log.error("Failed to refresh cluster topology", FutureUtils.unwrapCompletionException(t));
                    }
                })
                .toCompletableFuture();
    }
}
