package com.tui.destilink.framework.core.properties;

import com.tui.destilink.framework.core.util.FwPropertySourceUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;

public abstract class AbstractFwPostProcessor implements EnvironmentPostProcessor {

    protected static final String PROPERTY_SOURCES_NAME_PREFIX = "framework defaults:";

    protected static final String OVERRIDE_PROPERTY_SOURCES_NAME_PREFIX = "framework overrides:";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {
        doPostProcessEnvironment(env, application);
    }

    protected abstract void doPostProcessEnvironment(ConfigurableEnvironment env, SpringApplication application);

    protected String buildFwDefaultPropSourceName(String name) {
        return PROPERTY_SOURCES_NAME_PREFIX + this.getClass().getSimpleName() + ":" + "[" + name + "]";
    }

    protected String buildFwOverridePropSourceName(String name) {
        return OVERRIDE_PROPERTY_SOURCES_NAME_PREFIX + this.getClass().getSimpleName() + ":" + "[" + name + "]";
    }

    protected FwCompositePropertySource getFwCompositePropertySource(ConfigurableEnvironment env) {
        return FwPropertySourceUtils.getPropertySource(env);
    }

}
