package com.tui.destilink.framework.core.logging.filter.mute;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;
import com.tui.destilink.framework.core.logging.util.ThrowableUtils;
import org.slf4j.Marker;

public class LogMutingExceptionTurboFilter extends TurboFilter {
    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        Throwable result = ThrowableUtils.findThrowableInstanceOf(t, params, LogMutingException.class);
        if (result != null) {
            return FilterReply.DENY;
        }
        return FilterReply.NEUTRAL;
    }
}
