package com.tui.destilink.framework.redis.core.serializer;

import jakarta.annotation.Nullable;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;


public class RedisGzipSerializer<T> implements RedisSerializer<T> {
    private final RedisSerializer<T> delegate;

    public RedisGzipSerializer(RedisSerializer<T> delegate) {
        this.delegate = delegate;
    }

    @Nullable
    public static byte[] compress(@Nullable byte[] data) throws IOException {
        if (data != null) {
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream(data.length)) {
                try (GZIPOutputStream gzip = new GZIPOutputStream(bos)) {
                    gzip.write(data);
                }
                return bos.toByteArray();
            }
        }
        return null; // NOSONAR Da ist ein @Nullable dran du Arsch
    }

    @Nullable
    public static byte[] decompress(final byte[] compressed) throws IOException {
        if (compressed != null) {
            try (GZIPInputStream gis = new GZIPInputStream(new ByteArrayInputStream(compressed))) {
                return IOUtils.toByteArray(gis);
            }
        }
        return null; // NOSONAR Da ist ein @Nullable dran du Arsch
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        try {
            return compress(delegate.serialize(t));
        } catch (IOException e) {
            throw new SerializationException("Error during compression", e);
        }
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        try {
            return delegate.deserialize(decompress(bytes));
        } catch (IOException e) {
            throw new SerializationException("Error during decompression", e);
        }
    }

    @Override
    public boolean canSerialize(Class<?> type) {
        return delegate.canSerialize(type);
    }

    @Override
    public Class<?> getTargetType() {
        return delegate.getTargetType();
    }
}

