package com.tui.destilink.framework.core.env;

import com.tui.destilink.framework.core.util.DatadogEnvUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
@ConfigurationProperties(FwEnvironmentProperties.PREFIX)
public class FwEnvironmentProperties {

    public static final String PREFIX = "destilink.fw.core.env";

    @NotBlank
    @Pattern(regexp = "^(test-support|local|dev|test|release|prod)$")
    private String name = DatadogEnvUtils.resolveEnv();

}
