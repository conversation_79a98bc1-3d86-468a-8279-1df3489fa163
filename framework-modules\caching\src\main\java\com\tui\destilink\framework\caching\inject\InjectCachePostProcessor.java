package com.tui.destilink.framework.caching.inject;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringValueResolver;

public class InjectCachePostProcessor implements BeanPostProcessor, BeanFactoryAware, EmbeddedValueResolverAware {

    private ConfigurableListableBeanFactory beanFactory;
    private StringValueResolver resolver;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = (ConfigurableListableBeanFactory) beanFactory;
    }

    @Override
    public void setEmbeddedValueResolver(StringValueResolver resolver) {
        this.resolver = resolver;
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        try {
            Class<?> managedBeanClass = bean.getClass();
            ReflectionUtils.FieldCallback fieldCallback =
                    new InjectCacheFieldCallback(beanFactory, resolver, beanName, bean);
            ReflectionUtils.doWithFields(managedBeanClass, fieldCallback);
        } catch (BeansException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new BeanInitializationException("Failed to inject cache on bean " + beanName, ex);
        }

        return BeanPostProcessor.super.postProcessBeforeInitialization(bean, beanName);
    }

}
