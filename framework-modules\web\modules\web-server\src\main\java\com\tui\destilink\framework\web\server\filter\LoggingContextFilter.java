package com.tui.destilink.framework.web.server.filter;

import jakarta.servlet.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
public class LoggingContextFilter implements Filter {

    private final Map<String, String> additionalProperties = new HashMap<>();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            for (Map.Entry<String, String> entry : additionalProperties.entrySet())
                MDC.put(entry.getKey(), entry.getValue());

            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

    public void add(String key, String value) {
        additionalProperties.put(key, value);
    }

    public void addAll(Map<String, String> properties) {
        additionalProperties.putAll(properties);
    }

    public void remove(String key) {
        additionalProperties.remove(key);
    }
}