package com.tui.destilink.framework.web.server.error;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.net.URI;
import java.net.URISyntaxException;

@Slf4j
@ControllerAdvice
public class ProblemDetailsExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(Exception.class)
    protected ResponseEntity<Object> handleGenericException(Exception ex, WebRequest request) {
        ProblemDetail problemDetail = ProblemDetail.forStatusAndDetail(HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage());
        problemDetail.setTitle("An unexpected exception occurred");
        problemDetail.setInstance(resolveInstance(request));
        return handleExceptionInternal(ex, problemDetail, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, request);
    }

    protected URI resolveInstance(WebRequest request) {
        if (request instanceof ServletWebRequest swr) {
            final String requestURI = swr.getRequest().getRequestURI();
            try {
                return new URI(swr.getRequest().getRequestURI());
            } catch (URISyntaxException | NullPointerException ex) {
                log.error("Unable to resolve problem details instance fro URI {}", requestURI, ex);
            }
        }
        return null;
    }

}
