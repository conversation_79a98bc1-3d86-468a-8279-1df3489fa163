package com.tui.destilink.framework.cloudevents.publish;

import com.tui.destilink.framework.cloudevents.context.extension.tui.business.TuiBusinessExtension.TuiBusinessExtensionBuilder;
import com.tui.destilink.framework.cloudevents.context.extension.tui.map.GenericMapExtension.GenericMapExtensionBuilder;
import com.tui.destilink.framework.cloudevents.context.extension.tui.pd.TuiPdExtension.TuiPdExtensionBuilder;
import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import jakarta.validation.ConstraintViolation;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.slf4j.MDC;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.function.Function;

@Getter
@SuperBuilder(toBuilder = true)
public abstract class CloudEventsPublisherContext<T, R extends CloudEventsPublishResult> {

    public static final String DEFAULT_DATA_CONTENT_TYPE = "application/json";

    private final Class<T> payloadClazz;
    private final Class<R> resultClazz;

    private final String type;
    private final URI dataSchema;

    private Function<T, Set<ConstraintViolation<T>>> dataValidator;
    @Builder.Default
    private Function<T, String> id = t -> UUID.randomUUID().toString();
    private Function<T, URI> source;
    private Function<T, String> subject;
    private Function<T, OffsetDateTime> time;
    @Builder.Default
    private Function<T, String> dataContentType = t -> DEFAULT_DATA_CONTENT_TYPE;
    @Builder.Default
    private BiFunction<T, TuiBusinessExtensionBuilder, TuiBusinessExtensionBuilder> tuiBusinessExtensionBuilder = (t, b) ->
            b.businessProcessId(Optional.ofNullable(MDC.get(TripsContextConstants.BUSINESS_PROCESS_ID))
                            .map(UUID::fromString).orElse(null))
                    .businessProcessName(MDC.get(TripsContextConstants.BUSINESS_PROCESS_NAME));
    @Builder.Default
    private BiFunction<T, TuiPdExtensionBuilder, TuiPdExtensionBuilder> tuiPdExtensionBuilder = (t, b) -> b;
    @Builder.Default
    private BiFunction<T, GenericMapExtensionBuilder, GenericMapExtensionBuilder> customExtensionAttributes = (t, b) -> b;

    private final CloudEventsPublisherDataRefTarget dataRefTarget;
}
