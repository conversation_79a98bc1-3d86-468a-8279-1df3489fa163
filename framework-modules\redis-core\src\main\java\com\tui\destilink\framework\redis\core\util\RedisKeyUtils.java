package com.tui.destilink.framework.redis.core.util;

import lombok.experimental.UtilityClass;

import java.util.Collection;

@UtilityClass
public class RedisKeyUtils {

    public static final String KEY_DELIMITER = ":";

    private static final String HASH_TAG_PATTERN = "{%s}";

    public static String join(String... elements) {
        return String.join(KEY_DELIMITER, elements);
    }

    public static String join(Collection<String> elements) {
        return String.join(KEY_DELIMITER, elements);
    }

    public static String joinHashTag(String... elements) {
        return String.format(HASH_TAG_PATTERN, join(elements));
    }

    public static String joinHashTag(Collection<String> elements) {
        return String.format(HASH_TAG_PATTERN, join(elements));
    }
}
