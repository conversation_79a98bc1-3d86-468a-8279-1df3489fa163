package com.tui.destilink.framework.file.download.impl;

import com.tui.destilink.framework.file.download.FileDownloadService;
import lombok.NonNull;

import java.util.function.BiFunction;
import java.util.function.BinaryOperator;

public abstract class AbstractFileDownloadService implements FileDownloadService {

    protected BiFunction<String, String, String> filenameAdapter;

    @Override
    public void setFilenameAdapter(@NonNull BinaryOperator<String> filenameAdapter) {
        this.filenameAdapter = filenameAdapter;
    }
}
