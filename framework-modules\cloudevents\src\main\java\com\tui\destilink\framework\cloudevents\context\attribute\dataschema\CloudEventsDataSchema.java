package com.tui.destilink.framework.cloudevents.context.attribute.dataschema;

import lombok.Getter;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Getter
public class CloudEventsDataSchema {

    private static final String VALIDATE_REGEX = "^((.*)\\/(\\d*))(\\.(\\d*))?(-draft)?$";
    private static final Pattern VALIDATE_PATTERN = Pattern.compile(VALIDATE_REGEX);

    private final URI dataSchema;
    private final URI dataSchemaMajor;

    private final int majorVersion;
    private final Integer minorVersion;
    private final Boolean isDraft;

    private final Class<?> pojo;

    public CloudEventsDataSchema(URI dataSchema, URI dataSchemaMajor, int majorVersion, Integer minorVersion, Boolean isDraft, Class<?> pojo) {
        this.dataSchema = dataSchema;
        this.dataSchemaMajor = dataSchemaMajor;
        this.majorVersion = majorVersion;
        this.minorVersion = minorVersion;
        this.isDraft = isDraft;
        this.pojo = pojo;
    }

    public static CloudEventsDataSchema of(URI dataSchema) {
        return of(dataSchema, null);
    }

    public static CloudEventsDataSchema of(URI dataSchema, Class<?> pojo) {
        Matcher matcher = validate(dataSchema);
        try {
            URI dataSchemaMajor = findDataSchemaMajor(matcher);
            int majorVersion = findMajor(matcher);
            Integer minorVersion = findMinor(matcher);
            Boolean isDraft = findDraft(matcher);
            return new CloudEventsDataSchema(dataSchema, dataSchemaMajor, majorVersion, minorVersion, isDraft, pojo);
        } catch (Exception ex) {
            throw new IllegalArgumentException("Failed to parse dataSchema " + dataSchema, ex);
        }
    }

    public static Matcher validate(URI dataSchema) {
        Matcher matcher = VALIDATE_PATTERN.matcher(dataSchema.toString());
        if (matcher.find()) {
            return matcher;
        }
        throw new IllegalArgumentException("DataSchema " + dataSchema + " must match again regex " + VALIDATE_REGEX);
    }

    private static URI findDataSchemaMajor(Matcher matcher) {
        return URI.create(matcher.group(1));
    }

    private static int findMajor(Matcher matcher) {
        return Integer.parseInt(matcher.group(3));
    }

    private static Integer findMinor(Matcher matcher) {
        String res = matcher.group(5);
        if (res != null) {
            return Integer.parseInt(res);
        }
        return null;
    }

    private static Boolean findDraft(Matcher matcher) {
        String res = matcher.group(6);
        if (res != null) {
            return StringUtils.endsWithIgnoreCase(res, "-draft");
        }
        return false;
    }
}
