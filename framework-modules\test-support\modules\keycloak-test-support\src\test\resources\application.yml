spring:
  security:
    oauth2:
      client:
        registration:
          hello-tech-client-1:
            client-id: ${test-support.keycloak.tech-client.hello-tech-client-1.client-id}
            client-secret: ${test-support.keycloak.tech-client.hello-tech-client-1.client-secret}
destilink:
  test:
    user:
      hello-user-1:
        username: ${test-support.keycloak.user.hello-user-1.username}
        password: ${test-support.keycloak.user.hello-user-1.password}

logging:
  level:
    com:
      tui:
        destilink: debug