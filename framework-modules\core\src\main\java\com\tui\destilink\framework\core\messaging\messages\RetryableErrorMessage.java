package com.tui.destilink.framework.core.messaging.messages;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.ErrorMessage;

@EqualsAndHashCode(callSuper = true)
public class RetryableErrorMessage extends ErrorMessage {

    public static class NullThrowable extends Throwable {
    }

    private static final NullThrowable NULL_THROWABLE_INSTANCE = new NullThrowable();

    public static final boolean RETRYABLE_DEFAULT = true;

    @Getter
    private final boolean retryable;

    public RetryableErrorMessage(boolean retryable) {
        this(retryable, NULL_THROWABLE_INSTANCE);
    }

    public RetryableErrorMessage(Throwable throwable) {
        this(RETRYABLE_DEFAULT, throwable);
    }

    public RetryableErrorMessage(boolean retryable, Throwable throwable) {
        super(throwable);
        this.retryable = retryable;
    }

    public boolean hasThrowable() {
        return !(getPayload() instanceof NullThrowable);
    }

    public static boolean isRetryableErrorMessage(Message<?> message) {
        return message instanceof RetryableErrorMessage;
    }

    public static boolean hasThrowable(Message<?> message) {
        return message instanceof RetryableErrorMessage rem && !(rem.getPayload() instanceof NullThrowable);
    }
}
