package com.tui.destilink.framework.redis.core.util;

import org.springframework.data.redis.listener.ChannelTopic;

public class <PERSON>is<PERSON>ey extends AbstractRedisKey<RedisKey> {

    protected RedisKey(String value) {
        super(value);
    }

    public String getKey() {
        return getValue();
    }

    public ChannelTopic toChannelTopic() {
        return new ChannelTopic(getKey());
    }

    public byte[] getKeyBytes() {
        return CODEC.encodeKey(getKey()).array();
    }

    @Override
    public String toString() {
        return getKey();
    }

    @Override
    protected RedisKey newObject(String value) {
        return new RedisKey(value);
    }

    public RedisKeyPrefix toRedisKeyPrefix() {
        return new RedisKeyPrefix(getValue());
    }
}
