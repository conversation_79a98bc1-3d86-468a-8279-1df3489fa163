destilink:
  fw:
    core:
      metrics:
        statsd:
          enableFilter: true
          nonFilteredMetrics: [ ]
logging:
  level:
    io.micrometer.registry.otlp.OtlpMeterRegistry: ERROR
management:
  opentelemetry:
    resource-attributes:
      deployment.environment: ${DD_ENV:UNKNOWN_OTEL_ENV}
      service.name: ${DD_SERVICE:UNKNOWN_OTEL_SERVICE_NAME}
      service.version: ${DD_VERSION:UNKNOWN_OTEL_SERVICE_VERSION}
  otlp:
    metrics:
      export:
        enabled: true
        step: 10s
        connect-timeout: PT2S
        aggregation-temporality: cumulative
        url: ${OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:http://localhost:4318/v1/metrics}
  statsd:
    metrics:
      export:
        enabled: true
        host: ${DD_JMXFETCH_STATSD_HOST}
        protocol: uds_datagram